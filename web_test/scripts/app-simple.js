// Simple Mithril.js Application for Testing
// This is a minimal version to test the setup

// Simple state object
const appState = {
    currentView: 'watchlist',
    currentSymbol: 'Watchlist',
    openTabs: [
        { id: 'Watchlist', label: 'Watchlist', closable: false, active: true }
    ],
    watchlists: {
        '<PERSON>ân hàng': ['TCB', 'VCB', 'BID', 'CTG'],
        'Bất động sản': ['VIC', 'VHM', 'VRE'],
        'Thép': ['HPG']
    },
    selectedWatchlists: ['<PERSON><PERSON> hàng', 'Bất động sản'],
    toasts: []
};

// Simple Sidebar Component
const Sidebar = {
    view: () => {
        return m('aside.bg-sidebar.border-r.overflow-hidden', {
            style: 'width: 60px;'
        }, [
            m('div.flex.h-full.w-full.flex-col', [
                // Header with logo
                m('div.p-2', [
                    m('div.flex.aspect-square.size-8.items-center.justify-center.rounded-lg.bg-sidebar-primary.text-sidebar-primary-foreground', [
                        m('svg[xmlns="http://www.w3.org/2000/svg"][height="24px"][viewBox="0 -960 960 960"][width="24px"][fill="#FFFFFF"]', [
                            m('path[d="M368.5-195q-14.81 0-26.53-8.72-11.73-8.73-17.2-22.21L237.39-450H60.77v-60h218.77l88.92 230.92 179.39-455.38q5.45-13.48 17.14-22.2 11.7-8.72 26.51-8.72 14.81 0 26.53 8.72 11.73 8.72 17.2 22.2L723.38-510H900v60H681.23l-89.69-231.31-179.39 455.38q-5.45 13.48-17.14 22.21-11.7 8.72-26.51 8.72Z"]')
                        ])
                    ])
                ]),
                
                // Navigation items
                m('div.flex.min-h-0.flex-1.flex-col.gap-2.overflow-auto', [
                    m('div.p-2', [
                        m('ul.flex.w-full.flex-col.gap-1', [
                            // Watchlist button
                            m('li.flex.flex-col.items-center.text-center', [
                                m('button[type="button"].bg-accent.text-sm.aspect-square.size-8.p-1.cursor-pointer', {
                                    title: 'Watchlist'
                                }, [
                                    m('svg[xmlns="http://www.w3.org/2000/svg"][height="24px"][viewBox="0 -960 960 960"][width="24px"][fill="#1f1f1f"]', [
                                        m('path[d="M130-130v-65.77l60-60V-130h-60Zm160 0v-225.77l60-60V-130h-60Zm160 0v-285.77l60 61V-130h-60Zm160 0v-224.77l60-60V-130h-60Zm160 0v-385.77l60-60V-130h-60ZM130-351.23v-84.54l270-270 160 160 270-270v84.54l-270 270-160-160-270 270Z"]')
                                    ])
                                ])
                            ])
                        ])
                    ])
                ])
            ])
        ]);
    }
};

// Simple TopBar Component
const TopBar = {
    view: () => {
        return m('div.bg-sidebar.border-b', [
            m('div.tabs-container.overflow-x-auto.bg-background', [
                m('div[role="tablist"].flex.h-10.items-center', 
                    appState.openTabs.map(tab => 
                        m('div[role="tab"].flex.items-center.h-10.px-4.gap-2.cursor-pointer.border-r', {
                            'data-state': tab.active ? 'active' : undefined
                        }, [
                            m('span', tab.label)
                        ])
                    )
                )
            ])
        ]);
    }
};

// Simple Watchlist View
const WatchlistView = {
    view: () => {
        return m('div', [
            m('h1.text-2xl.font-bold.mb-4', 'Danh sách theo dõi'),
            m('div.bg-card.rounded-lg.border.p-4', [
                m('p', 'Watchlist functionality will be implemented here.'),
                m('div.mt-4', [
                    m('h3.font-semibold.mb-2', 'Selected Watchlists:'),
                    m('ul.list-disc.list-inside',
                        appState.selectedWatchlists.map(name =>
                            m('li', `${name} (${appState.watchlists[name].length} stocks)`)
                        )
                    )
                ])
            ])
        ]);
    }
};

// Main Layout Component
const MainLayout = {
    view: () => {
        return m('div.flex.flex-1.h-screen', [
            // Sidebar
            m(Sidebar),
            
            // Main Content Area
            m('div.flex.flex-col.w-full.overflow-hidden', [
                // Top Bar
                m(TopBar),
                
                // Main Content
                m('main.flex-1.p-4.overflow-auto', [
                    m(WatchlistView)
                ])
            ])
        ]);
    }
};

// Mount the application
m.mount(document.body, MainLayout);

console.log('Simple Mithril.js app loaded successfully!');
