// Application State Management
// Centralized state management for the Mithril.js application

export class AppState {
    constructor() {
        // Current view state
        this.currentView = 'watchlist'; // 'watchlist' or 'stock'
        this.currentSymbol = 'Watchlist';
        
        // Tab management
        this.openTabs = [
            { id: 'Watchlist', label: 'Watchlist', closable: false, active: true }
        ];
        
        // Watchlist state
        this.watchlists = {};
        this.selectedWatchlists = [];
        this.currentSelectedWatchlistId = null;
        
        // Stock data cache
        this.stockDataCache = {};
        this.priceHistoryCache = {};
        this.indicatorDataCache = {};
        
        // UI state
        this.sidebarCollapsed = false;
        this.chartSectionsState = {};
        this.chartOverlays = {};
        
        // Dialog states
        this.createWatchlistDialogOpen = false;
        this.watchlistManagerDialogOpen = false;
        this.dialogState = {
            isEditMode: false,
            editingWatchlistId: null,
            selectedSymbols: []
        };
        
        // Toast notifications
        this.toasts = [];
        this.toastIdCounter = 0;
        
        // Initialize from localStorage
        this.loadFromStorage();
        
        // Sample stock data
        this.sampleStocks = [
            { symbol: 'HPG', name: 'Hòa Phát Group', price: '21.55', change: '+0.55', changePercent: '+2.62%', trend: 'up' },
            { symbol: 'VNM', name: 'Vinamilk', price: '71.20', change: '-0.30', changePercent: '-0.42%', trend: 'down' },
            { symbol: 'TCB', name: 'Techcombank', price: '32.45', change: '+0.75', changePercent: '*****%', trend: 'up' },
            { symbol: 'VCB', name: 'Vietcombank', price: '89.90', change: '*****', changePercent: '*****%', trend: 'up' },
            { symbol: 'VIC', name: 'Vingroup', price: '43.25', change: '-0.65', changePercent: '-1.48%', trend: 'down' },
            { symbol: 'FPT', name: 'FPT Corp', price: '112.80', change: '*****', changePercent: '*****%', trend: 'up' },
            { symbol: 'MWG', name: 'Mobile World', price: '53.10', change: '-0.90', changePercent: '-1.67%', trend: 'down' },
            { symbol: 'VHM', name: 'Vinhomes', price: '47.35', change: '+0.25', changePercent: '+0.53%', trend: 'up' },
            { symbol: 'MSN', name: 'Masan Group', price: '74.60', change: '-1.40', changePercent: '-1.84%', trend: 'down' },
            { symbol: 'VRE', name: 'Vincom Retail', price: '25.75', change: '+0.45', changePercent: '*****%', trend: 'up' }
        ];
    }
    
    // Load state from localStorage
    loadFromStorage() {
        const storedWatchlists = localStorage.getItem('watchlists');
        if (storedWatchlists) {
            this.watchlists = JSON.parse(storedWatchlists);
        } else {
            // Default watchlists
            this.watchlists = {
                'Ngân hàng': ['TCB', 'VCB', 'BID', 'CTG'],
                'Bất động sản': ['VIC', 'VHM', 'VRE'],
                'Thép': ['HPG']
            };
            this.saveWatchlists();
        }
        
        const storedSelectedWatchlists = localStorage.getItem('selectedWatchlists');
        const availableWatchlistIds = Object.keys(this.watchlists);
        
        if (storedSelectedWatchlists) {
            const savedSelection = JSON.parse(storedSelectedWatchlists);
            this.selectedWatchlists = savedSelection.filter(id => availableWatchlistIds.includes(id));
        } else {
            this.selectedWatchlists = [...availableWatchlistIds];
        }
        
        if (availableWatchlistIds.length > 0) {
            this.currentSelectedWatchlistId = availableWatchlistIds[0];
        }
        
        // Load chart sections state
        const storedSectionsState = localStorage.getItem('chartSectionsState');
        if (storedSectionsState) {
            this.chartSectionsState = JSON.parse(storedSectionsState);
        }
    }
    
    // Save watchlists to localStorage
    saveWatchlists() {
        localStorage.setItem('watchlists', JSON.stringify(this.watchlists));
    }
    
    // Save selected watchlists to localStorage
    saveSelectedWatchlists() {
        localStorage.setItem('selectedWatchlists', JSON.stringify(this.selectedWatchlists));
    }
    
    // Save chart sections state
    saveChartSectionsState() {
        localStorage.setItem('chartSectionsState', JSON.stringify(this.chartSectionsState));
    }
    
    // Tab management methods
    addTab(symbol) {
        const existingTab = this.openTabs.find(tab => tab.id === symbol);
        if (existingTab) {
            this.selectTab(symbol);
            return;
        }
        
        this.openTabs.push({
            id: symbol,
            label: symbol,
            closable: true,
            active: false
        });
        
        this.selectTab(symbol);
    }
    
    selectTab(tabId) {
        this.openTabs.forEach(tab => {
            tab.active = tab.id === tabId;
        });
        
        this.currentSymbol = tabId;
        this.currentView = tabId === 'Watchlist' ? 'watchlist' : 'stock';
        
        m.redraw();
    }
    
    closeTab(tabId) {
        const tabIndex = this.openTabs.findIndex(tab => tab.id === tabId);
        if (tabIndex === -1) return;
        
        const wasActive = this.openTabs[tabIndex].active;
        this.openTabs.splice(tabIndex, 1);
        
        if (wasActive && this.openTabs.length > 0) {
            const newActiveTab = this.openTabs[this.openTabs.length - 1];
            this.selectTab(newActiveTab.id);
        }
    }
    
    // Toast management
    showToast(message, type = 'info', duration = 3000) {
        const toast = {
            id: `toast-${++this.toastIdCounter}`,
            message,
            type,
            visible: true
        };
        
        this.toasts.push(toast);
        m.redraw();
        
        if (duration > 0) {
            setTimeout(() => {
                this.removeToast(toast.id);
            }, duration);
        }
        
        return toast.id;
    }
    
    removeToast(toastId) {
        const toastIndex = this.toasts.findIndex(toast => toast.id === toastId);
        if (toastIndex !== -1) {
            this.toasts.splice(toastIndex, 1);
            m.redraw();
        }
    }
    
    // Chart resize trigger
    triggerChartResize() {
        // This will be implemented when we create the stock component
        m.redraw();
    }
    
    // Watchlist management methods
    toggleWatchlistSelection(watchlistId) {
        const index = this.selectedWatchlists.indexOf(watchlistId);
        
        if (index === -1) {
            this.selectedWatchlists.push(watchlistId);
        } else {
            this.selectedWatchlists.splice(index, 1);
        }
        
        this.saveSelectedWatchlists();
        m.redraw();
    }
    
    // Dialog management
    openCreateWatchlistDialog() {
        this.createWatchlistDialogOpen = true;
        this.dialogState = {
            isEditMode: false,
            editingWatchlistId: null,
            selectedSymbols: []
        };
        m.redraw();
    }
    
    closeCreateWatchlistDialog() {
        this.createWatchlistDialogOpen = false;
        this.dialogState = {
            isEditMode: false,
            editingWatchlistId: null,
            selectedSymbols: []
        };
        m.redraw();
    }
    
    openWatchlistManager() {
        this.watchlistManagerDialogOpen = true;
        m.redraw();
    }
    
    closeWatchlistManager() {
        this.watchlistManagerDialogOpen = false;
        m.redraw();
    }
}
