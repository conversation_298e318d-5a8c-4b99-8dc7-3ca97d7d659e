// Data Transformation Utilities
// Converts server data format to UI-compatible format

export class DataTransformer {
    /**
     * Transform stock analysis data from server format to UI format
     * @param {Object} serverData - Raw data from server (e.g., HPG.json)
     * @returns {Object} Transformed data for UI consumption
     */
    static transformStockAnalysis(serverData) {
        if (!serverData) return null;

        return {
            symbol: serverData.s,
            analysisDate: serverData.ad,
            lastTradingDate: serverData.ltd,
            price: {
                current: serverData.p?.c || 0,
                change: serverData.p?.cv || 0,
                changePercent: serverData.p?.cp || 0
            },
            trend: {
                direction: serverData.t?.d || 'neutral',
                strength: serverData.t?.s || 'unknown',
                confidence: serverData.t?.c || '0%'
            },
            marketCondition: serverData.mc || 'unknown',
            recommendation: {
                action: serverData.r?.r || 'Hold',
                summary: serverData.r?.s || 'No analysis available'
            },
            zones: {
                buy: serverData.bz || [],
                stopLoss: serverData.slz || [],
                takeProfit: serverData.tpz || []
            },
            riskReward: serverData.rr || [],
            technicalIndicators: serverData.ti || []
        };
    }

    /**
     * Transform price history data from server format
     * @param {Object} priceData - Raw price data (e.g., HPG_prices.json)
     * @returns {Array} Array of price points for charting
     */
    static transformPriceHistory(priceData) {
        if (!priceData || !priceData.t) return [];

        const { t: timestamps, o: opens, h: highs, l: lows, c: closes, v: volumes } = priceData;
        
        return timestamps.map((timestamp, index) => ({
            time: timestamp,
            open: opens[index],
            high: highs[index],
            low: lows[index],
            close: closes[index],
            volume: volumes ? volumes[index] : 0
        }));
    }

    /**
     * Transform technical indicator data
     * @param {Object} indicatorData - Raw indicator data (e.g., HPG_rsi.json)
     * @param {string} indicatorType - Type of indicator (rsi, macd, etc.)
     * @returns {Array} Transformed indicator data
     */
    static transformTechnicalIndicator(indicatorData, indicatorType) {
        if (!indicatorData || !indicatorData.t) return [];

        const { t: timestamps } = indicatorData;
        
        switch (indicatorType.toLowerCase()) {
            case 'rsi':
                return timestamps.map((timestamp, index) => ({
                    time: timestamp,
                    value: indicatorData.v[index]
                }));
                
            case 'macd':
                return timestamps.map((timestamp, index) => ({
                    time: timestamp,
                    macd: indicatorData.macd?.[index],
                    signal: indicatorData.signal?.[index],
                    histogram: indicatorData.histogram?.[index]
                }));
                
            case 'bb': // Bollinger Bands
                return timestamps.map((timestamp, index) => ({
                    time: timestamp,
                    upper: indicatorData.u?.[index],
                    middle: indicatorData.m?.[index],
                    lower: indicatorData.l?.[index],
                    width: indicatorData.w?.[index]
                }));
                
            case 'stochastic':
                return timestamps.map((timestamp, index) => ({
                    time: timestamp,
                    k: indicatorData.k?.[index],
                    d: indicatorData.d?.[index]
                }));
                
            case 'ma': // Moving Averages
                return timestamps.map((timestamp, index) => ({
                    time: timestamp,
                    sma5: indicatorData.s5?.[index],
                    ema5: indicatorData.e5?.[index],
                    sma10: indicatorData.s10?.[index],
                    ema10: indicatorData.e10?.[index],
                    sma20: indicatorData.s20?.[index],
                    ema20: indicatorData.e20?.[index],
                    sma50: indicatorData.s50?.[index],
                    ema50: indicatorData.e50?.[index],
                    sma100: indicatorData.s100?.[index],
                    ema100: indicatorData.e100?.[index],
                    sma200: indicatorData.s200?.[index],
                    ema200: indicatorData.e200?.[index]
                }));
                
            default:
                // Generic indicator with single value
                return timestamps.map((timestamp, index) => ({
                    time: timestamp,
                    value: indicatorData.v?.[index]
                }));
        }
    }

    /**
     * Format timestamp to readable date
     * @param {number} timestamp - Unix timestamp
     * @returns {string} Formatted date string
     */
    static formatTimestamp(timestamp) {
        return new Date(timestamp * 1000).toLocaleDateString('vi-VN');
    }

    /**
     * Format price with Vietnamese currency format
     * @param {number} price - Price value
     * @returns {string} Formatted price string
     */
    static formatPrice(price) {
        if (!price) return 'N/A';
        return new Intl.NumberFormat('vi-VN').format(price);
    }

    /**
     * Format percentage with proper sign and color class
     * @param {number} percent - Percentage value
     * @returns {Object} Object with formatted text and CSS class
     */
    static formatPercentage(percent) {
        if (percent === null || percent === undefined) {
            return { text: 'N/A', class: 'text-muted-foreground' };
        }
        
        const formatted = `${percent >= 0 ? '+' : ''}${(percent * 100).toFixed(2)}%`;
        const cssClass = percent >= 0 ? 'text-green-600' : 'text-red-600';
        
        return { text: formatted, class: cssClass };
    }

    /**
     * Get trend icon based on direction
     * @param {string} direction - Trend direction
     * @returns {string} Material icon name
     */
    static getTrendIcon(direction) {
        switch (direction?.toLowerCase()) {
            case 'tăng':
            case 'up':
                return 'trending_up';
            case 'giảm':
            case 'down':
                return 'trending_down';
            case 'sideway':
            case 'sideways':
            case 'neutral':
                return 'trending_flat';
            default:
                return 'help';
        }
    }

    /**
     * Get trend color class based on direction
     * @param {string} direction - Trend direction
     * @returns {string} CSS class name
     */
    static getTrendColorClass(direction) {
        switch (direction?.toLowerCase()) {
            case 'tăng':
            case 'up':
                return 'text-green-600';
            case 'giảm':
            case 'down':
                return 'text-red-600';
            case 'sideway':
            case 'sideways':
            case 'neutral':
                return 'text-gray-600';
            default:
                return 'text-muted-foreground';
        }
    }

    /**
     * Validate data structure
     * @param {Object} data - Data to validate
     * @param {string} type - Type of data (analysis, prices, indicator)
     * @returns {boolean} True if valid
     */
    static validateData(data, type) {
        if (!data) return false;
        
        switch (type) {
            case 'analysis':
                return data.s && data.p && data.t;
            case 'prices':
                return data.t && data.o && data.h && data.l && data.c;
            case 'indicator':
                return data.t && (data.v || data.k || data.macd);
            default:
                return false;
        }
    }

    /**
     * Generate mock data for missing indicators
     * @param {string} symbol - Stock symbol
     * @param {string} indicatorType - Type of indicator
     * @returns {Object} Mock indicator data
     */
    static generateMockIndicatorData(symbol, indicatorType) {
        const now = Date.now() / 1000;
        const timestamps = Array.from({ length: 30 }, (_, i) => now - (29 - i) * 86400);
        
        switch (indicatorType.toLowerCase()) {
            case 'rsi':
                return {
                    t: timestamps,
                    v: timestamps.map(() => 30 + Math.random() * 40)
                };
            case 'macd':
                return {
                    t: timestamps,
                    macd: timestamps.map(() => (Math.random() - 0.5) * 100),
                    signal: timestamps.map(() => (Math.random() - 0.5) * 80),
                    histogram: timestamps.map(() => (Math.random() - 0.5) * 50)
                };
            default:
                return {
                    t: timestamps,
                    v: timestamps.map(() => Math.random() * 100)
                };
        }
    }
}

// Export utility functions for direct use
export const {
    transformStockAnalysis,
    transformPriceHistory,
    transformTechnicalIndicator,
    formatTimestamp,
    formatPrice,
    formatPercentage,
    getTrendIcon,
    getTrendColorClass,
    validateData,
    generateMockIndicatorData
} = DataTransformer;
