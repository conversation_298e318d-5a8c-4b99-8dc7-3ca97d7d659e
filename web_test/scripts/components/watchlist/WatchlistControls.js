// WatchlistControls Component
// Controls for watchlist management including dropdown, search, and action buttons

export const WatchlistControls = {
    oninit: (vnode) => {
        vnode.state.dropdownOpen = false;
        vnode.state.searchValue = '';
        vnode.state.autocompleteOpen = false;
    },
    
    view: (vnode) => {
        const { state } = vnode.attrs;
        const localState = vnode.state;
        
        return m('div.mb-6', [
            // Header with title and action buttons
            m('div.flex.items-center.justify-between.mb-4', [
                m('h1.text-2xl.font-bold.text-foreground', '<PERSON>h sách theo dõi'),
                
                m('div.flex.gap-2', [
                    // Create new watchlist button
                    m('button.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.rounded-md.text-sm.font-medium.h-9.px-4.py-2.bg-primary.text-primary-foreground.hover\\:bg-primary\\/90', {
                        onclick: () => state.openCreateWatchlistDialog()
                    }, [
                        m('svg[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                            m('path[d="M12 5v14M5 12h14"]')
                        ]),
                        'Tạo mới'
                    ]),
                    
                    // Manage watchlists button
                    m('button.inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.rounded-md.text-sm.font-medium.h-9.px-4.py-2.border.border-input.bg-background.text-accent-foreground.hover\\:bg-accent.hover\\:text-accent-foreground', {
                        onclick: () => state.openWatchlistManager()
                    }, [
                        m('svg[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                            m('path[d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"]'),
                            m('circle[cx="12"][cy="12"][r="3"]')
                        ]),
                        'Danh sách'
                    ])
                ])
            ]),
            
            // Watchlist selection and search controls
            m('div.flex.flex-col.sm\\:flex-row.gap-4.mb-4', [
                // Watchlist dropdown
                m('div.relative.flex-1', [
                    m('button.flex.h-10.w-full.items-center.justify-between.rounded-md.border.border-input.bg-background.px-3.py-2.text-sm.ring-offset-background.placeholder\\:text-muted-foreground.focus\\:outline-none.focus\\:ring-2.focus\\:ring-ring.focus\\:ring-offset-2', {
                        onclick: () => {
                            localState.dropdownOpen = !localState.dropdownOpen;
                        }
                    }, [
                        m('div.flex.items-center.gap-2.flex-1.min-w-0', [
                            m('span.text-sm.text-muted-foreground', 'Danh sách:'),
                            m('div#selected-watchlists.flex.items-center.gap-1.flex-wrap', [
                                state.selectedWatchlists.length === 0 ? 
                                    m('span#watchlist-placeholder.text-sm.text-muted-foreground', 'Chọn danh sách theo dõi') :
                                    state.selectedWatchlists.length > 2 ?
                                        [
                                            m(WatchlistBadge, { 
                                                id: state.selectedWatchlists[0], 
                                                onRemove: () => state.toggleWatchlistSelection(state.selectedWatchlists[0])
                                            }),
                                            m('span.inline-flex.items-center.rounded-md.bg-primary.px-2.py-1.text-xs.font-medium.text-primary-foreground', 
                                                `+${state.selectedWatchlists.length - 1}`)
                                        ] :
                                        state.selectedWatchlists.map(id => 
                                            m(WatchlistBadge, { 
                                                id, 
                                                onRemove: () => state.toggleWatchlistSelection(id)
                                            })
                                        )
                            ])
                        ]),
                        m('svg.h-4.w-4.opacity-50[xmlns="http://www.w3.org/2000/svg"][width="24"][height="24"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                            m('path[d="m6 9 6 6 6-6"]')
                        ])
                    ]),
                    
                    // Dropdown content
                    localState.dropdownOpen && m('div#watchlist-dropdown.absolute.z-50.w-full.min-w-\\[8rem\\].overflow-hidden.rounded-md.border.bg-background.p-1.text-foreground.shadow-md.mt-1', 
                        Object.keys(state.watchlists).length === 0 ?
                            m('div.relative.flex.cursor-default.select-none.items-center.rounded-sm.py-2.px-2.text-sm.outline-none.text-muted-foreground', 
                                'No watchlists available') :
                            Object.keys(state.watchlists).map(id => m(WatchlistDropdownItem, { 
                                id, 
                                state, 
                                selected: state.selectedWatchlists.includes(id),
                                onToggle: () => state.toggleWatchlistSelection(id),
                                onEdit: () => {
                                    localState.dropdownOpen = false;
                                    // Open edit dialog - will be implemented
                                }
                            }))
                    )
                ]),
                
                // Search input
                m('div.relative.flex-1', [
                    m('input#watchlist-search.flex.h-10.w-full.rounded-md.border.border-input.bg-background.px-3.py-2.text-sm.ring-offset-background.file\\:border-0.file\\:bg-transparent.file\\:text-sm.file\\:font-medium.placeholder\\:text-muted-foreground.focus-visible\\:outline-none.focus-visible\\:ring-2.focus-visible\\:ring-ring.focus-visible\\:ring-offset-2[type="text"][placeholder="Thêm mã cổ phiếu (ví dụ: HPG)"]', {
                        value: localState.searchValue,
                        oninput: (e) => {
                            localState.searchValue = e.target.value;
                            localState.autocompleteOpen = e.target.value.trim().length > 0;
                        },
                        onkeydown: (e) => {
                            if (e.key === 'Enter' && localState.searchValue.trim()) {
                                e.preventDefault();
                                addSymbolToSelectedWatchlists(state, localState.searchValue.trim().toUpperCase());
                                localState.searchValue = '';
                                localState.autocompleteOpen = false;
                            }
                        }
                    }),
                    
                    // Autocomplete dropdown
                    localState.autocompleteOpen && m(AutocompleteDropdown, { 
                        query: localState.searchValue,
                        stocks: state.sampleStocks,
                        onSelect: (symbol) => {
                            addSymbolToSelectedWatchlists(state, symbol);
                            localState.searchValue = '';
                            localState.autocompleteOpen = false;
                        }
                    })
                ])
            ])
        ]);
    }
};

// Helper component for watchlist badges
const WatchlistBadge = {
    view: (vnode) => {
        const { id, onRemove } = vnode.attrs;
        
        return m('span.inline-flex.items-center.rounded-md.bg-secondary.px-2.py-1.text-xs.font-medium.text-secondary-foreground', [
            m('span', id),
            m('button.ml-1.rounded-full.text-secondary-foreground.hover\\:bg-secondary-foreground\\/20', {
                onclick: (e) => {
                    e.stopPropagation();
                    onRemove();
                }
            }, [
                m('svg[xmlns="http://www.w3.org/2000/svg"][width="12"][height="12"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                    m('path[d="M18 6 6 18"]'),
                    m('path[d="m6 6 12 12"]')
                ])
            ])
        ]);
    }
};

// Helper component for dropdown items
const WatchlistDropdownItem = {
    view: (vnode) => {
        const { id, state, selected, onToggle, onEdit } = vnode.attrs;
        
        return m('div.flex.justify-between.cursor-default.select-none.items-center.rounded-sm.py-2.pl-2.pr-2.text-sm.outline-none.hover\\:bg-accent.hover\\:text-accent-foreground[data-value="' + id + '"]', [
            // Checkbox and text
            m('div.flex.items-center.flex-1.ml-2', {
                onclick: onToggle
            }, [
                m('span.flex.h-3\\.5.w-3\\.5.items-center.justify-center', [
                    m('button[type="button"][role="checkbox"].peer.h-4.w-4.shrink-0.rounded-sm.border.border-primary.shadow.focus-visible\\:outline-none.focus-visible\\:ring-1.focus-visible\\:ring-ring', {
                        'aria-checked': selected ? 'true' : 'false',
                        'data-state': selected ? 'checked' : undefined
                    }, selected && m('svg[xmlns="http://www.w3.org/2000/svg"][width="14px"][height="14px"][viewBox="0 -960 960 960"][fill="#000000"]', [
                        m('path[d="M382-253.85 168.62-467.23 211.38-510 382-339.38 748.62-706l42.76 42.77L382-253.85Z"]')
                    ]))
                ]),
                m('span.ml-2', id)
            ]),
            
            // Edit button
            m('button.ml-2.text-muted-foreground.hover\\:text-foreground', {
                onclick: (e) => {
                    e.stopPropagation();
                    onEdit();
                }
            }, [
                m('svg[xmlns="http://www.w3.org/2000/svg"][width="12"][height="12"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                    m('path[d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"]'),
                    m('path[d="m15 5 4 4"]')
                ])
            ])
        ]);
    }
};

// Helper component for autocomplete dropdown
const AutocompleteDropdown = {
    view: (vnode) => {
        const { query, stocks, onSelect } = vnode.attrs;
        const filteredSymbols = stocks
            .map(stock => stock.symbol)
            .filter(symbol => symbol.includes(query.toUpperCase()));
        
        return m('div#search-autocomplete.absolute.z-50.w-full.min-w-\\[8rem\\].overflow-hidden.rounded-md.border.bg-background.p-1.text-foreground.shadow-md.mt-1', 
            filteredSymbols.length === 0 ?
                m('div.py-6.text-center.text-sm.text-muted-foreground', 'No results found') :
                filteredSymbols.map(symbol => 
                    m('div.relative.flex.cursor-default.select-none.items-center.rounded-sm.px-4.py-2.text-sm.outline-none.hover\\:bg-accent.hover\\:text-accent-foreground', {
                        onclick: () => onSelect(symbol)
                    }, symbol)
                )
        );
    }
};

// Helper function to add symbol to selected watchlists
function addSymbolToSelectedWatchlists(state, symbol) {
    if (state.selectedWatchlists.length === 0) {
        state.showToast('Please select at least one watchlist first.', 'warning');
        return;
    }
    
    if (!/^[A-Z0-9]{1,10}$/.test(symbol)) {
        state.showToast('Invalid symbol format. Please use 1-10 uppercase letters and numbers (e.g., AAPL, HPG).', 'error');
        return;
    }
    
    let addedToAny = false;
    
    state.selectedWatchlists.forEach(watchlistId => {
        if (!state.watchlists[watchlistId].includes(symbol)) {
            state.watchlists[watchlistId].push(symbol);
            state.watchlists[watchlistId].sort();
            addedToAny = true;
        }
    });
    
    if (addedToAny) {
        state.saveWatchlists();
        state.showToast(`Added ${symbol} to selected watchlists.`, 'success');
        m.redraw();
    } else {
        state.showToast(`Symbol ${symbol} is already in all selected watchlists.`, 'warning');
    }
}
