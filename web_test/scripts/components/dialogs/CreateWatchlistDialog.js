// CreateWatchlistDialog Component
// Dialog for creating and editing watchlists

export const CreateWatchlistDialog = {
    oninit: (vnode) => {
        vnode.state.watchlistName = '';
        vnode.state.nameError = '';
        vnode.state.symbolSearch = '';
        vnode.state.autocompleteOpen = false;
        vnode.state.selectedSymbols = [...vnode.attrs.state.dialogState.selectedSymbols];
    },
    
    view: (vnode) => {
        const { state } = vnode.attrs;
        const localState = vnode.state;
        const isEditMode = state.dialogState.isEditMode;
        
        return m('div.fixed.inset-0.z-50.bg-background\\/80.backdrop-blur-sm.flex.items-center.justify-center', [
            m('div[role="dialog"][data-state="open"].relative.z-50.grid.w-full.max-w-lg.gap-4.border.bg-background.p-6.shadow-lg', [
                // Header
                m('div.flex.flex-col.space-y-1\\.5.text-center.sm\\:text-left', [
                    m('h3.text-lg.font-semibold.leading-none.tracking-tight', 
                        isEditMode ? 'Sửa danh sách theo dõi' : 'Tạo danh sách theo dõi'
                    ),
                    m('p.text-sm.text-muted-foreground', 'Nhập tên và chọn mã cổ phiếu cho danh sách theo dõi của bạn')
                ]),
                
                // Content
                m('div.grid.gap-4.py-4', [
                    // Watchlist name input
                    m('div.grid.gap-2', [
                        m('label.text-sm.font-medium.leading-none[for="newWatchlistNameInput"]', 'Tên danh sách theo dõi'),
                        m('div.relative', [
                            m('input#newWatchlistNameInput.flex.h-10.w-full.rounded-md.border.border-input.bg-background.px-3.py-2.text-sm.ring-offset-background.placeholder\\:text-muted-foreground.focus-visible\\:outline-none.focus-visible\\:ring-2.focus-visible\\:ring-ring.focus-visible\\:ring-offset-2[placeholder="To the moon"]', {
                                value: localState.watchlistName,
                                oninput: (e) => {
                                    localState.watchlistName = e.target.value;
                                    validateWatchlistName(localState, state);
                                }
                            }),
                            localState.nameError && m('div.text-sm.text-red-600.text-destructive.mt-1', localState.nameError)
                        ])
                    ]),
                    
                    // Symbol search input
                    m('div.grid.gap-2', [
                        m('label.text-sm.font-medium.leading-none[for="symbolSearchInput"]', 'Tìm mã cổ phiếu'),
                        m('div.relative', [
                            m('input#symbolSearchInput.flex.h-10.w-full.rounded-md.border.border-input.bg-background.px-3.py-2.text-sm.ring-offset-background.focus-visible\\:outline-none.focus-visible\\:ring-2.focus-visible\\:ring-ring.focus-visible\\:ring-offset-2[type="text"][placeholder="Nhập mã cổ phiếu cần thêm vào danh sách. (ví dụ: HPG)"]', {
                                value: localState.symbolSearch,
                                oninput: (e) => {
                                    localState.symbolSearch = e.target.value;
                                    localState.autocompleteOpen = e.target.value.trim().length > 0;
                                },
                                onkeydown: (e) => {
                                    if (e.key === 'Enter' && localState.symbolSearch.trim()) {
                                        e.preventDefault();
                                        addSymbolToDialog(localState.symbolSearch.trim().toUpperCase(), localState);
                                        localState.symbolSearch = '';
                                        localState.autocompleteOpen = false;
                                    }
                                }
                            }),
                            
                            // Autocomplete dropdown
                            localState.autocompleteOpen && m(SymbolAutocomplete, {
                                query: localState.symbolSearch,
                                stocks: state.sampleStocks,
                                selectedSymbols: localState.selectedSymbols,
                                onSelect: (symbol) => {
                                    addSymbolToDialog(symbol, localState);
                                    localState.symbolSearch = '';
                                    localState.autocompleteOpen = false;
                                }
                            })
                        ])
                    ]),
                    
                    // Selected symbols display
                    m('div.grid.gap-2', [
                        m('label.text-sm.font-medium.leading-none', 'Mã cổ phiếu đã chọn'),
                        m('div.relative', [
                            m('div.flex.flex-wrap.gap-1.p-2.border.rounded-md.min-h-\\[100px\\].max-h-\\[200px\\].overflow-y-auto', [
                                m('div.flex.flex-wrap.gap-1.w-full',
                                    localState.selectedSymbols.map(symbol => m(SelectedSymbolBadge, {
                                        symbol,
                                        onRemove: () => {
                                            const index = localState.selectedSymbols.indexOf(symbol);
                                            if (index !== -1) {
                                                localState.selectedSymbols.splice(index, 1);
                                            }
                                        }
                                    }))
                                )
                            ])
                        ])
                    ])
                ]),
                
                // Footer buttons
                m('div.flex.justify-end.gap-2', [
                    m('button[type="button"].whitespace-nowrap.rounded-md.text-sm.font-medium.transition-colors.border.border-input.bg-background.text-accent-foreground.h-10.px-4.py-2.cursor-pointer', {
                        onclick: () => state.closeCreateWatchlistDialog()
                    }, 'Hủy'),
                    
                    m('button[type="button"].whitespace-nowrap.rounded-md.text-sm.font-medium.transition-colors.border.border-input.bg-primary.text-primary-foreground.h-10.px-4.py-2.cursor-pointer', {
                        onclick: () => handleSaveWatchlist(localState, state)
                    }, isEditMode ? 'Cập nhật' : 'Tạo')
                ])
            ])
        ]);
    }
};

const SymbolAutocomplete = {
    view: (vnode) => {
        const { query, stocks, selectedSymbols, onSelect } = vnode.attrs;
        const filteredSymbols = stocks
            .map(stock => stock.symbol)
            .filter(symbol => symbol.includes(query.toUpperCase()));
        
        return m('div.absolute.z-50.w-full.min-w-\\[8rem\\].overflow-hidden.rounded-md.border.bg-background.p-1.text-foreground.shadow-md.mt-1',
            filteredSymbols.length === 0 ?
                m('div.py-6.text-center.text-sm.text-muted-foreground', 'No results found') :
                filteredSymbols.map(symbol => {
                    const isSelected = selectedSymbols.includes(symbol);
                    
                    return m('div.relative.flex.cursor-default.select-none.items-center.rounded-sm.py-2.pl-8.pr-2.text-sm.outline-none.hover\\:bg-accent.hover\\:text-accent-foreground', {
                        onclick: () => onSelect(symbol)
                    }, [
                        // Check icon for selected symbols
                        m('span.absolute.left-2.flex.h-3\\.5.w-3\\.5.items-center.justify-center',
                            isSelected && m('svg[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"].h-4.w-4', [
                                m('polyline[points="20 6 9 17 4 12"]')
                            ])
                        ),
                        symbol
                    ]);
                })
        );
    }
};

const SelectedSymbolBadge = {
    view: (vnode) => {
        const { symbol, onRemove } = vnode.attrs;
        
        return m('span.inline-flex.items-center.rounded-md.bg-secondary.px-2.py-1.text-xs.font-medium.text-secondary-foreground', [
            symbol,
            m('button[type="button"].ml-1.rounded-full.text-secondary-foreground.hover\\:bg-secondary-foreground\\/20', {
                onclick: onRemove
            }, [
                m('svg[xmlns="http://www.w3.org/2000/svg"][width="12"][height="12"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                    m('path[d="M18 6 6 18"]'),
                    m('path[d="m6 6 12 12"]')
                ])
            ])
        ]);
    }
};

function validateWatchlistName(localState, state) {
    const name = localState.watchlistName.trim();
    
    if (!name) {
        localState.nameError = 'Hãy nhập tên cho danh sách theo dõi';
        return false;
    }
    
    // In edit mode, if the name hasn't changed, it's valid
    if (state.dialogState.isEditMode && name === state.dialogState.editingWatchlistId) {
        localState.nameError = '';
        return true;
    }
    
    if (state.watchlists.hasOwnProperty(name)) {
        localState.nameError = 'Tên này đã được sử dụng';
        return false;
    }
    
    localState.nameError = '';
    return true;
}

function addSymbolToDialog(symbol, localState) {
    if (!/^[A-Z0-9]{1,10}$/.test(symbol)) {
        return;
    }
    
    if (!localState.selectedSymbols.includes(symbol)) {
        localState.selectedSymbols.push(symbol);
        localState.selectedSymbols.sort();
    }
}

function handleSaveWatchlist(localState, state) {
    if (!validateWatchlistName(localState, state)) {
        return;
    }
    
    const newName = localState.watchlistName.trim();
    
    if (state.dialogState.isEditMode) {
        const editingId = state.dialogState.editingWatchlistId;
        if (editingId && editingId !== newName) {
            // Name changed, create new and delete old
            state.watchlists[newName] = localState.selectedSymbols;
            delete state.watchlists[editingId];
            
            // Update selected watchlists
            const index = state.selectedWatchlists.indexOf(editingId);
            if (index !== -1) {
                state.selectedWatchlists[index] = newName;
            }
        } else {
            // Just update symbols
            state.watchlists[editingId] = localState.selectedSymbols;
        }
    } else {
        // Create new watchlist
        state.watchlists[newName] = localState.selectedSymbols;
        state.selectedWatchlists.push(newName);
    }
    
    state.saveWatchlists();
    state.saveSelectedWatchlists();
    state.closeCreateWatchlistDialog();
    state.showToast(
        state.dialogState.isEditMode ? 'Watchlist updated successfully!' : 'Watchlist created successfully!',
        'success'
    );
}
