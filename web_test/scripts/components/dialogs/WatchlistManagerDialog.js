// WatchlistManagerDialog Component
// Comprehensive watchlist management interface

export const WatchlistManagerDialog = {
    oninit: (vnode) => {
        vnode.state.selectedWatchlistId = null;
        vnode.state.hasUnsavedChanges = false;
        vnode.state.localWatchlists = JSON.parse(JSON.stringify(vnode.attrs.state.watchlists));
    },
    
    view: (vnode) => {
        const { state } = vnode.attrs;
        const localState = vnode.state;
        
        return m('div.fixed.inset-0.z-50.bg-background\\/80.backdrop-blur-sm.flex.items-center.justify-center', [
            m('div[role="dialog"][data-state="open"].relative.z-50.grid.w-full.max-w-4xl.gap-4.border.bg-background.p-6.shadow-lg.max-h-\\[80vh\\].overflow-hidden', [
                // Header
                m('div.flex.flex-col.space-y-1\\.5.text-center.sm\\:text-left', [
                    m('h3.text-lg.font-semibold.leading-none.tracking-tight', 'Qu<PERSON>n lý danh sách theo dõi'),
                    m('p.text-sm.text-muted-foreground', 'Tạo, chỉnh sửa và quản lý các danh sách theo dõi của bạn')
                ]),
                
                // Content - Two column layout
                m('div.grid.grid-cols-1.md\\:grid-cols-2.gap-6.flex-1.overflow-hidden', [
                    // Left Column: Watchlist Management
                    m('div.flex.flex-col.gap-4', [
                        m('div.flex.items-center.justify-between', [
                            m('h4.text-sm.font-medium', 'Danh sách theo dõi'),
                            m('button[type="button"].inline-flex.items-center.justify-center.gap-2.whitespace-nowrap.rounded-md.text-sm.font-medium.h-8.px-3.bg-primary.text-primary-foreground.hover\\:bg-primary\\/90', {
                                onclick: () => {
                                    // Add new watchlist functionality
                                    const newName = prompt('Tên danh sách mới:');
                                    if (newName && newName.trim()) {
                                        localState.localWatchlists[newName.trim()] = [];
                                        localState.hasUnsavedChanges = true;
                                    }
                                }
                            }, [
                                m('svg[xmlns="http://www.w3.org/2000/svg"][width="14"][height="14"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                                    m('path[d="M12 5v14M5 12h14"]')
                                ]),
                                'Thêm mới'
                            ])
                        ]),
                        
                        // Watchlist list
                        m('div.flex-1.overflow-y-auto.border.rounded-md.p-2.min-h-\\[300px\\].max-h-\\[400px\\]',
                            Object.keys(localState.localWatchlists).length === 0 ?
                                m('div.flex.flex-col.items-center.justify-center.h-full.text-center.p-4', [
                                    m('p.text-sm.text-muted-foreground', 'Chưa có danh sách theo dõi nào'),
                                    m('p.text-xs.text-muted-foreground.mt-1', 'Nhấn "Thêm mới" để tạo danh sách đầu tiên')
                                ]) :
                                Object.keys(localState.localWatchlists).map(watchlistId => 
                                    m(WatchlistManagerItem, {
                                        watchlistId,
                                        symbolCount: localState.localWatchlists[watchlistId].length,
                                        selected: localState.selectedWatchlistId === watchlistId,
                                        onSelect: () => {
                                            localState.selectedWatchlistId = watchlistId;
                                        },
                                        onDelete: () => {
                                            if (confirm(`Bạn có chắc muốn xóa danh sách "${watchlistId}"?`)) {
                                                delete localState.localWatchlists[watchlistId];
                                                if (localState.selectedWatchlistId === watchlistId) {
                                                    localState.selectedWatchlistId = null;
                                                }
                                                localState.hasUnsavedChanges = true;
                                            }
                                        }
                                    })
                                )
                        )
                    ]),
                    
                    // Right Column: Symbol Management
                    m('div.flex.flex-col.gap-4', [
                        m('div.flex.items-center.justify-between', [
                            m('h4.text-sm.font-medium', 'Mã cổ phiếu'),
                            m('span.text-sm.text-muted-foreground', 
                                localState.selectedWatchlistId ? localState.selectedWatchlistId : 'Chọn danh sách bên trái'
                            )
                        ]),
                        
                        // Symbol input
                        m('div.grid.gap-2', [
                            m('input[type="text"][placeholder="Nhập mã cổ phiếu (ví dụ: HPG)"].flex.h-10.w-full.rounded-md.border.border-input.bg-background.px-3.py-2.text-sm.ring-offset-background.focus-visible\\:outline-none.focus-visible\\:ring-2.focus-visible\\:ring-ring.focus-visible\\:ring-offset-2', {
                                onkeydown: (e) => {
                                    if (e.key === 'Enter' && e.target.value.trim() && localState.selectedWatchlistId) {
                                        const symbol = e.target.value.trim().toUpperCase();
                                        if (!/^[A-Z0-9]{1,10}$/.test(symbol)) {
                                            alert('Invalid symbol format');
                                            return;
                                        }
                                        
                                        if (!localState.localWatchlists[localState.selectedWatchlistId].includes(symbol)) {
                                            localState.localWatchlists[localState.selectedWatchlistId].push(symbol);
                                            localState.localWatchlists[localState.selectedWatchlistId].sort();
                                            localState.hasUnsavedChanges = true;
                                            e.target.value = '';
                                        }
                                    }
                                }
                            })
                        ]),
                        
                        // Symbols list
                        m('div.flex-1.overflow-y-auto.border.rounded-md.p-2.min-h-\\[300px\\].max-h-\\[400px\\]',
                            !localState.selectedWatchlistId ?
                                m('div.flex.flex-col.items-center.justify-center.h-full.text-center.p-4', [
                                    m('p.text-sm.text-muted-foreground', 'Chọn danh sách bên trái'),
                                    m('p.text-xs.text-muted-foreground.mt-1', 'để xem và chỉnh sửa mã cổ phiếu')
                                ]) :
                                localState.localWatchlists[localState.selectedWatchlistId].length === 0 ?
                                    m('div.flex.flex-col.items-center.justify-center.h-full.text-center.p-4', [
                                        m('p.text-sm.text-muted-foreground', 'Danh sách trống'),
                                        m('p.text-xs.text-muted-foreground.mt-1', 'Thêm mã cổ phiếu bằng ô nhập bên trên')
                                    ]) :
                                    localState.localWatchlists[localState.selectedWatchlistId].map(symbol =>
                                        m('div.flex.items-center.justify-between.p-3.mb-2.rounded-md.border.hover\\:bg-muted.transition-colors', [
                                            m('div.flex-1', [
                                                m('div.font-medium.text-sm', symbol)
                                            ]),
                                            m('button[type="button"].p-1.rounded-md.text-muted-foreground.hover\\:text-destructive.hover\\:bg-destructive\\/10.transition-colors', {
                                                onclick: () => {
                                                    const index = localState.localWatchlists[localState.selectedWatchlistId].indexOf(symbol);
                                                    if (index !== -1) {
                                                        localState.localWatchlists[localState.selectedWatchlistId].splice(index, 1);
                                                        localState.hasUnsavedChanges = true;
                                                    }
                                                }
                                            }, [
                                                m('svg[xmlns="http://www.w3.org/2000/svg"][width="14"][height="14"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                                                    m('path[d="M18 6 6 18M6 6l12 12"]')
                                                ])
                                            ])
                                        ])
                                    )
                        )
                    ])
                ]),
                
                // Footer buttons
                m('div.flex.justify-end.gap-2', [
                    m('button[type="button"].whitespace-nowrap.rounded-md.text-sm.font-medium.transition-colors.border.border-input.bg-background.text-accent-foreground.h-10.px-4.py-2.cursor-pointer', {
                        onclick: () => {
                            if (localState.hasUnsavedChanges) {
                                if (!confirm('Bạn có thay đổi chưa được lưu. Bạn có muốn đóng mà không lưu?')) {
                                    return;
                                }
                            }
                            state.closeWatchlistManager();
                        }
                    }, 'Đóng'),
                    
                    m('button[type="button"].whitespace-nowrap.rounded-md.text-sm.font-medium.transition-colors.border.border-input.bg-primary.text-primary-foreground.h-10.px-4.py-2.cursor-pointer', {
                        onclick: () => {
                            // Save changes
                            state.watchlists = localState.localWatchlists;
                            state.saveWatchlists();
                            
                            // Update selected watchlists to remove any that no longer exist
                            const availableIds = Object.keys(state.watchlists);
                            state.selectedWatchlists = state.selectedWatchlists.filter(id => availableIds.includes(id));
                            state.saveSelectedWatchlists();
                            
                            state.closeWatchlistManager();
                            state.showToast('Watchlists updated successfully!', 'success');
                        }
                    }, 'Lưu thay đổi')
                ])
            ])
        ]);
    }
};

const WatchlistManagerItem = {
    view: (vnode) => {
        const { watchlistId, symbolCount, selected, onSelect, onDelete } = vnode.attrs;
        
        return m(`div.flex.items-center.justify-between.p-3.mb-2.rounded-md.border.cursor-pointer.transition-colors.${selected ? 'bg-accent border-primary' : 'hover:bg-muted'}`, {
            onclick: onSelect
        }, [
            m('div.flex-1.min-w-0', [
                m('div.font-medium.text-sm.truncate', watchlistId),
                m('div.text-xs.text-muted-foreground', `${symbolCount} mã cổ phiếu`)
            ]),
            m('button[type="button"].p-1.rounded-md.text-muted-foreground.hover\\:text-destructive.hover\\:bg-destructive\\/10.transition-colors', {
                onclick: (e) => {
                    e.stopPropagation();
                    onDelete();
                }
            }, [
                m('svg[xmlns="http://www.w3.org/2000/svg"][width="14"][height="14"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                    m('path[d="M3 6h18M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6m3 0V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"]'),
                    m('line[x1="10"][x2="10"][y1="11"][y2="17"]'),
                    m('line[x1="14"][x2="14"][y1="11"][y2="17"]')
                ])
            ])
        ]);
    }
};
