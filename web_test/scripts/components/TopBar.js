// TopBar Component
// Top bar with tab management for selected stocks

export const TopBar = {
    view: (vnode) => {
        const { state } = vnode.attrs;
        
        return m('div#main-topbar-wrapper.bg-sidebar.border-b', [
            // Tab container
            m('div.tabs-container.overflow-x-auto.bg-background.overscroll-contain.scrollbar-thin.scrollbar-track-rounded', [
                m('div#selectedSymbolsPlaceholder[role="tablist"].flex.h-10.items-center', 
                    state.openTabs.map(tab => m(TabItem, { 
                        tab, 
                        state,
                        onclick: () => state.selectTab(tab.id),
                        onclose: tab.closable ? () => state.closeTab(tab.id) : null
                    }))
                )
            ])
        ]);
    }
};

const TabItem = {
    view: (vnode) => {
        const { tab, state, onclick, onclose } = vnode.attrs;
        
        return m('div[role="tab"].flex.items-center.h-10.px-4.pr-2.pt-2.gap-2.cursor-pointer.border-r', {
            'data-value': tab.id,
            'data-state': tab.active ? 'active' : undefined,
            onclick: onclick
        }, [
            // Tab title
            m('span', tab.label),
            
            // Close button (if closable)
            onclose && m('a.size-6.rounded-full.flex.items-center.justify-center.cursor-pointer', {
                onclick: (e) => {
                    e.stopPropagation();
                    onclose();
                }
            }, [
                m('svg[xmlns="http://www.w3.org/2000/svg"][width="14"][height="14"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                    m('path[d="M18 6 6 18"]'),
                    m('path[d="m6 6 12 12"]')
                ])
            ])
        ]);
    }
};
