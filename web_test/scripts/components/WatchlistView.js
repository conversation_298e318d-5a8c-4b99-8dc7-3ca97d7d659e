// WatchlistView Component
// Displays the watchlist interface with stock table and management controls

import { WatchlistControls } from './watchlist/WatchlistControls.js';
import { WatchlistTable } from './watchlist/WatchlistTable.js';

export const WatchlistView = {
    view: (vnode) => {
        const { state } = vnode.attrs;
        
        return m('div#watchlistView', [
            // Watchlist Controls (dropdown, search, etc.)
            m(WatchlistControls, { state }),
            
            // Watchlist Table
            m(WatchlistTable, { state })
        ]);
    }
};
