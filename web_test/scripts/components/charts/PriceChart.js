// PriceChart Component
// Displays stock price chart using Lightweight Charts

import { DataTransformer } from '../../utils/DataTransformer.js';

export const PriceChart = {
    oncreate: (vnode) => {
        const { symbol, state } = vnode.attrs;
        initializeChart(vnode.dom, symbol, state);
    },
    
    onupdate: (vnode) => {
        const { symbol, state } = vnode.attrs;
        if (vnode.state.lastSymbol !== symbol) {
            vnode.state.lastSymbol = symbol;
            updateChart(vnode.dom, symbol, state);
        }
    },
    
    onremove: (vnode) => {
        if (vnode.state.chart) {
            vnode.state.chart.remove();
        }
    },
    
    view: (vnode) => {
        const { symbol } = vnode.attrs;
        
        return m('div.chart-container', [
            m('div.chart-header.mb-4', [
                m('h3.text-lg.font-semibold', `${symbol} - <PERSON>i<PERSON>u đồ giá`),
                m('div.flex.gap-2.mt-2', [
                    m('button.px-3.py-1.text-xs.bg-primary.text-primary-foreground.rounded', '1D'),
                    m('button.px-3.py-1.text-xs.bg-muted.text-muted-foreground.rounded.hover\\:bg-muted\\/80', '1W'),
                    m('button.px-3.py-1.text-xs.bg-muted.text-muted-foreground.rounded.hover\\:bg-muted\\/80', '1M'),
                    m('button.px-3.py-1.text-xs.bg-muted.text-muted-foreground.rounded.hover\\:bg-muted\\/80', '3M'),
                    m('button.px-3.py-1.text-xs.bg-muted.text-muted-foreground.rounded.hover\\:bg-muted\\/80', '1Y')
                ])
            ]),
            m('div#chart-container', {
                style: 'height: 400px; width: 100%;'
            })
        ]);
    }
};

function initializeChart(container, symbol, state) {
    if (!window.LightweightCharts) {
        console.error('Lightweight Charts library not loaded');
        return;
    }
    
    const chartContainer = container.querySelector('#chart-container');
    if (!chartContainer) return;
    
    // Create chart
    const chart = LightweightCharts.createChart(chartContainer, {
        width: chartContainer.clientWidth,
        height: 400,
        layout: {
            background: { color: 'transparent' },
            textColor: '#333',
        },
        grid: {
            vertLines: { color: '#e1e5e9' },
            horzLines: { color: '#e1e5e9' },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: '#cccccc',
        },
        timeScale: {
            borderColor: '#cccccc',
            timeVisible: true,
            secondsVisible: false,
        },
    });
    
    // Create candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        borderVisible: false,
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
    });
    
    // Store chart reference
    container.chart = chart;
    container.candlestickSeries = candlestickSeries;
    
    // Load and display data
    updateChart(container, symbol, state);
    
    // Handle resize
    const resizeObserver = new ResizeObserver(entries => {
        if (entries.length === 0 || entries[0].target !== chartContainer) return;
        const { width, height } = entries[0].contentRect;
        chart.applyOptions({ width, height });
    });
    
    resizeObserver.observe(chartContainer);
    container.resizeObserver = resizeObserver;
}

function updateChart(container, symbol, state) {
    if (!container.chart || !container.candlestickSeries) return;
    
    // Get price history from cache
    const priceHistory = state.priceHistoryCache[symbol];
    if (!priceHistory) {
        // Try to load price data
        loadPriceData(symbol, state).then(() => {
            updateChart(container, symbol, state);
        });
        return;
    }
    
    // Transform price data for chart
    const transformedData = DataTransformer.transformPriceHistory(priceHistory);
    if (!transformedData || transformedData.length === 0) {
        console.warn('No price data available for', symbol);
        return;
    }
    
    // Convert to chart format
    const chartData = transformedData.map(point => ({
        time: point.time,
        open: point.open,
        high: point.high,
        low: point.low,
        close: point.close
    }));
    
    // Update chart data
    container.candlestickSeries.setData(chartData);
    
    // Fit content
    container.chart.timeScale().fitContent();
}

async function loadPriceData(symbol, state) {
    if (!symbol || symbol === 'Watchlist') return;
    
    try {
        const response = await fetch(`analysis/${symbol}_prices.json`);
        const priceHistory = await response.json();
        state.priceHistoryCache[symbol] = priceHistory;
        m.redraw();
    } catch (error) {
        console.warn('Failed to load price data for', symbol, error);
    }
}

// Volume Chart Component
export const VolumeChart = {
    oncreate: (vnode) => {
        const { symbol, state } = vnode.attrs;
        initializeVolumeChart(vnode.dom, symbol, state);
    },
    
    onupdate: (vnode) => {
        const { symbol, state } = vnode.attrs;
        if (vnode.state.lastSymbol !== symbol) {
            vnode.state.lastSymbol = symbol;
            updateVolumeChart(vnode.dom, symbol, state);
        }
    },
    
    onremove: (vnode) => {
        if (vnode.state.chart) {
            vnode.state.chart.remove();
        }
    },
    
    view: (vnode) => {
        const { symbol } = vnode.attrs;
        
        return m('div.volume-chart-container', [
            m('div.chart-header.mb-2', [
                m('h4.text-md.font-medium', `${symbol} - Khối lượng giao dịch`)
            ]),
            m('div#volume-chart-container', {
                style: 'height: 150px; width: 100%;'
            })
        ]);
    }
};

function initializeVolumeChart(container, symbol, state) {
    if (!window.LightweightCharts) {
        console.error('Lightweight Charts library not loaded');
        return;
    }
    
    const chartContainer = container.querySelector('#volume-chart-container');
    if (!chartContainer) return;
    
    // Create volume chart
    const chart = LightweightCharts.createChart(chartContainer, {
        width: chartContainer.clientWidth,
        height: 150,
        layout: {
            background: { color: 'transparent' },
            textColor: '#333',
        },
        grid: {
            vertLines: { color: '#e1e5e9' },
            horzLines: { color: '#e1e5e9' },
        },
        rightPriceScale: {
            borderColor: '#cccccc',
        },
        timeScale: {
            borderColor: '#cccccc',
            timeVisible: true,
            secondsVisible: false,
        },
    });
    
    // Create histogram series for volume
    const volumeSeries = chart.addHistogramSeries({
        color: '#26a69a',
        priceFormat: {
            type: 'volume',
        },
        priceScaleId: '',
        scaleMargins: {
            top: 0.8,
            bottom: 0,
        },
    });
    
    // Store chart reference
    container.chart = chart;
    container.volumeSeries = volumeSeries;
    
    // Load and display data
    updateVolumeChart(container, symbol, state);
}

function updateVolumeChart(container, symbol, state) {
    if (!container.chart || !container.volumeSeries) return;
    
    // Get price history from cache
    const priceHistory = state.priceHistoryCache[symbol];
    if (!priceHistory) return;
    
    // Transform price data for volume chart
    const transformedData = DataTransformer.transformPriceHistory(priceHistory);
    if (!transformedData || transformedData.length === 0) return;
    
    // Convert to volume chart format
    const volumeData = transformedData.map(point => ({
        time: point.time,
        value: point.volume || 0,
        color: point.close >= point.open ? '#26a69a' : '#ef5350'
    }));
    
    // Update chart data
    container.volumeSeries.setData(volumeData);
    
    // Fit content
    container.chart.timeScale().fitContent();
}

// Simple Line Chart for indicators
export const IndicatorChart = {
    oncreate: (vnode) => {
        const { symbol, indicator, state } = vnode.attrs;
        initializeIndicatorChart(vnode.dom, symbol, indicator, state);
    },
    
    onupdate: (vnode) => {
        const { symbol, indicator, state } = vnode.attrs;
        const key = `${symbol}_${indicator}`;
        if (vnode.state.lastKey !== key) {
            vnode.state.lastKey = key;
            updateIndicatorChart(vnode.dom, symbol, indicator, state);
        }
    },
    
    onremove: (vnode) => {
        if (vnode.state.chart) {
            vnode.state.chart.remove();
        }
    },
    
    view: (vnode) => {
        const { symbol, indicator } = vnode.attrs;
        
        return m('div.indicator-chart-container', [
            m('div.chart-header.mb-2', [
                m('h4.text-md.font-medium', `${symbol} - ${indicator.toUpperCase()}`)
            ]),
            m('div#indicator-chart-container', {
                style: 'height: 200px; width: 100%;'
            })
        ]);
    }
};

function initializeIndicatorChart(container, symbol, indicator, state) {
    if (!window.LightweightCharts) {
        console.error('Lightweight Charts library not loaded');
        return;
    }
    
    const chartContainer = container.querySelector('#indicator-chart-container');
    if (!chartContainer) return;
    
    // Create indicator chart
    const chart = LightweightCharts.createChart(chartContainer, {
        width: chartContainer.clientWidth,
        height: 200,
        layout: {
            background: { color: 'transparent' },
            textColor: '#333',
        },
        grid: {
            vertLines: { color: '#e1e5e9' },
            horzLines: { color: '#e1e5e9' },
        },
        rightPriceScale: {
            borderColor: '#cccccc',
        },
        timeScale: {
            borderColor: '#cccccc',
            timeVisible: true,
            secondsVisible: false,
        },
    });
    
    // Create line series for indicator
    const lineSeries = chart.addLineSeries({
        color: '#2196F3',
        lineWidth: 2,
    });
    
    // Store chart reference
    container.chart = chart;
    container.lineSeries = lineSeries;
    
    // Load and display data
    updateIndicatorChart(container, symbol, indicator, state);
}

function updateIndicatorChart(container, symbol, indicator, state) {
    if (!container.chart || !container.lineSeries) return;
    
    // Try to load indicator data
    loadIndicatorData(symbol, indicator, state).then(() => {
        const indicatorData = state.indicatorDataCache[`${symbol}_${indicator}`];
        if (!indicatorData) return;
        
        // Transform indicator data
        const transformedData = DataTransformer.transformTechnicalIndicator(indicatorData, indicator);
        if (!transformedData || transformedData.length === 0) return;
        
        // Convert to chart format (simple line for now)
        const chartData = transformedData.map(point => ({
            time: point.time,
            value: point.value || point.rsi || point.macd || 0
        })).filter(point => point.value !== null && point.value !== undefined);
        
        // Update chart data
        container.lineSeries.setData(chartData);
        
        // Fit content
        container.chart.timeScale().fitContent();
    });
}

async function loadIndicatorData(symbol, indicator, state) {
    if (!symbol || symbol === 'Watchlist') return;
    
    const cacheKey = `${symbol}_${indicator}`;
    if (state.indicatorDataCache[cacheKey]) return;
    
    try {
        const response = await fetch(`analysis/${symbol}_${indicator}.json`);
        const indicatorData = await response.json();
        state.indicatorDataCache[cacheKey] = indicatorData;
        m.redraw();
    } catch (error) {
        console.warn('Failed to load indicator data for', symbol, indicator, error);
    }
}
