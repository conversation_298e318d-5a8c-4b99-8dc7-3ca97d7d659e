// StockDetailView Component
// Displays detailed stock analysis with charts and technical indicators

export const StockDetailView = {
    oninit: (vnode) => {
        const { state } = vnode.attrs;
        // Load stock data when component initializes
        loadStockData(state.currentSymbol, state);
    },
    
    onupdate: (vnode) => {
        const { state } = vnode.attrs;
        // Reload data if symbol changed
        if (vnode.state.lastSymbol !== state.currentSymbol) {
            vnode.state.lastSymbol = state.currentSymbol;
            loadStockData(state.currentSymbol, state);
        }
    },
    
    view: (vnode) => {
        const { state } = vnode.attrs;
        const stockData = state.stockDataCache[state.currentSymbol];
        
        if (!stockData) {
            return m('div.flex.h-full.items-center.justify-center', [
                m('div.text-center', [
                    m('div.animate-spin.rounded-full.h-8.w-8.border-b-2.border-primary.mx-auto.mb-4'),
                    m('p.text-muted-foreground', 'Loading stock data...')
                ])
            ]);
        }
        
        return m('div#stockDetailView.flex.h-full', [
            // Main Content Area
            m('div.flex-1.overflow-auto.pr-4', [
                // Stock Header
                m(StockHeader, { stockData, symbol: state.currentSymbol }),
                
                // Technical Analysis Summary
                m(AnalysisSummary, { stockData }),
                
                // Chart Sections
                m(ChartSections, { symbol: state.currentSymbol, state })
            ]),
            
            // Resizable Divider
            m('div.w-1.bg-border.hover\\:bg-primary.cursor-col-resize.transition-colors'),
            
            // Fixed Right Sidebar - Stock Signals
            m(StockSignalsSidebar, { stockData, state })
        ]);
    }
};

const StockHeader = {
    view: (vnode) => {
        const { stockData, symbol } = vnode.attrs;
        const changeClass = stockData.p.cp >= 0 ? 'text-green-600' : 'text-red-600';
        const changeIcon = stockData.p.cp >= 0 ? '↗' : '↘';
        
        return m('div#stockHeader.mb-6', [
            m('div.bg-card.rounded-lg.p-6', [
                m('div.flex.items-center.justify-between.mb-4', [
                    m('div.flex.items-center.gap-4', [
                        m('h1.text-3xl.font-bold.text-foreground', stockData.s),
                        m('div.text-2xl.font-semibold.text-foreground', stockData.p.c.toLocaleString()),
                        m(`div.flex.items-center.gap-1.${changeClass}`, [
                            m('span.text-lg', changeIcon),
                            m('span.text-lg.font-medium', `${stockData.p.cv} (${stockData.p.cp}%)`)
                        ])
                    ]),
                    m('div.text-sm.text-muted-foreground', `Cập nhật: ${stockData.ltd}`)
                ]),
                m('div.flex.items-center.gap-4.text-sm', [
                    m('span.px-3.py-1.rounded-full.bg-muted.text-muted-foreground', 
                        `Xu hướng: ${stockData.t.d} (${stockData.t.s})`),
                    m('span.px-3.py-1.rounded-full.bg-muted.text-muted-foreground', 
                        `Thị trường: ${stockData.mc}`),
                    m('span.px-3.py-1.rounded-full.bg-muted.text-muted-foreground', 
                        `Độ tin cậy: ${stockData.t.c}`)
                ])
            ])
        ]);
    }
};

const AnalysisSummary = {
    view: (vnode) => {
        const { stockData } = vnode.attrs;
        
        return m('div#analysisSummary.mb-6', [
            m('div.bg-card.border.border-border.rounded-lg.p-4', [
                m('h2.text-lg.font-semibold.mb-3.text-foreground', 'Tóm tắt phân tích kỹ thuật'),
                m('div.prose.prose-sm.max-w-none.text-foreground', 
                    m.trust(stockData.r.s.replace(/\n/g, '<br>'))
                ),
                stockData.ma && stockData.ma.length > 0 && m('div.mt-4.grid.grid-cols-2.md\\:grid-cols-3.gap-2',
                    stockData.ma.map(ma => 
                        m('div.rounded.p-2.bg-muted\\/30', [
                            m('div.font-medium.text-sm', `${ma.t} ${ma.p}`),
                            m('div.text-xs.text-muted-foreground', `${ma.v} [${ma.s}]`)
                        ])
                    )
                )
            ])
        ]);
    }
};

const ChartSections = {
    view: (vnode) => {
        const { symbol, state } = vnode.attrs;
        
        return m('div#chartSections.space-y-4', [
            // Price Analysis Section
            m(ChartSection, {
                id: 'price-analysis',
                title: 'Phân tích giá',
                icon: 'trending_up',
                symbol,
                state,
                content: m('div', [
                    // Chart overlay controls
                    m('div.mb-4.p-3.bg-muted\\/30.rounded-lg', [
                        m('div.flex.items-center.justify-between.mb-3', [
                            m('h4.text-sm.font-medium.text-foreground', 'Hiển thị trên biểu đồ giá'),
                            m('button[type="button"].text-xs.px-2.py-1.rounded.bg-muted.hover\\:bg-muted\\/80.transition-colors', 'Reset All')
                        ]),
                        m('div.flex.flex-wrap.gap-2', [
                            ['RSI', 'Bollinger Bands', 'Ichimoku', 'Moving Averages'].map(overlay =>
                                m('label.flex.items-center.gap-2.cursor-pointer', [
                                    m('input[type="checkbox"].rounded.border-border'),
                                    m('span.text-sm', overlay)
                                ])
                            )
                        ])
                    ]),
                    
                    // Main price chart
                    m('div.chart-container.mb-4', [
                        m('div#chart-price-history', 'Price chart will be rendered here')
                    ]),
                    
                    // Additional chart panels
                    m('div#additionalChartPanels.space-y-4.mb-4'),
                    
                    // Price analysis charts grid
                    m('div#priceAnalysisCharts.grid.grid-cols-1.lg\\:grid-cols-2.gap-4')
                ])
            }),
            
            // Other chart sections would be added here
            // Momentum, Volume, Trend sections...
        ]);
    }
};

const ChartSection = {
    view: (vnode) => {
        const { id, title, icon, content } = vnode.attrs;
        
        return m(`div.chart-section[data-section="${id}"]`, [
            m('div.chart-section-header', [
                m('h3.chart-section-title', [
                    m(`svg.chart-section-icon[xmlns="http://www.w3.org/2000/svg"][width="20"][height="20"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]`, [
                        m('path[d="M3 3v18h18"]'),
                        m('path[d="m19 9-5 5-4-4-3 3"]')
                    ]),
                    title
                ]),
                m('button.chart-section-toggle[type="button"]', {
                    title: `Thu gọn/Mở rộng phần ${title.toLowerCase()}`
                }, [
                    m('svg.chart-section-chevron[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                        m('path[d="m6 9 6 6 6-6"]')
                    ])
                ])
            ]),
            m('div.chart-section-content', content)
        ]);
    }
};

const StockSignalsSidebar = {
    view: (vnode) => {
        const { stockData, state } = vnode.attrs;
        
        return m('div#stockSignalsSidebar.bg-card.border-l.border-border.overflow-y-auto', {
            style: 'width: 320px;'
        }, [
            // Header
            m('div.sticky.top-0.bg-card.border-b.border-border.p-4.z-10', [
                m('div.flex.items-center.justify-between', [
                    m('h2.text-lg.font-semibold.text-foreground', 'Tín hiệu kỹ thuật'),
                    m('button[type="button"].p-1.rounded-md.hover\\:bg-muted.transition-colors', {
                        title: 'Thu gọn/Mở rộng sidebar',
                        onclick: () => {
                            state.sidebarCollapsed = !state.sidebarCollapsed;
                        }
                    }, [
                        m('svg[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                            m('path[d="m15 18-6-6 6-6"]')
                        ])
                    ])
                ])
            ]),
            
            // Content
            m('div.p-4.space-y-4', [
                // Overall Recommendation
                m('div#overallRecommendation.bg-muted\\/50.rounded-lg.p-4', [
                    m('h3.font-semibold.mb-2', 'Khuyến nghị tổng thể'),
                    m('p.text-sm.text-muted-foreground', 'Phân tích tín hiệu kỹ thuật sẽ được hiển thị ở đây.')
                ]),
                
                // Key Indicators
                m('div#keyIndicators.space-y-3', [
                    m('h3.font-semibold', 'Chỉ báo chính'),
                    m('p.text-sm.text-muted-foreground', 'Các chỉ báo kỹ thuật quan trọng sẽ được hiển thị ở đây.')
                ]),
                
                // Trading Zones
                m('div#tradingZones.space-y-3', [
                    m('h3.font-semibold', 'Vùng giao dịch'),
                    m('p.text-sm.text-muted-foreground', 'Thông tin về vùng mua/bán sẽ được hiển thị ở đây.')
                ])
            ])
        ]);
    }
};

// Helper function to load stock data
async function loadStockData(symbol, state) {
    if (!symbol || symbol === 'Watchlist') {
        return;
    }
    
    try {
        // Check cache first
        if (state.stockDataCache[symbol]) {
            return;
        }
        
        // Load main stock data
        const response = await fetch(`analysis/${symbol}.json`);
        const stockData = await response.json();
        
        state.stockDataCache[symbol] = stockData;
        m.redraw();
        
        // Load price history
        try {
            const priceResponse = await fetch(`analysis/${symbol}_prices.json`);
            const priceHistory = await priceResponse.json();
            state.priceHistoryCache[symbol] = priceHistory;
            m.redraw();
        } catch (e) {
            console.log('No price history available for', symbol);
        }
        
    } catch (error) {
        console.error('Failed to load stock data for', symbol, error);
        state.showToast(`Failed to load data for ${symbol}`, 'error');
    }
}
