// StockDetailView Component
// Displays detailed stock analysis with charts and technical indicators

import { DataTransformer } from '../utils/DataTransformer.js';

export const StockDetailView = {
    oninit: (vnode) => {
        const { state } = vnode.attrs;
        // Load stock data when component initializes
        loadStockData(state.currentSymbol, state);
    },

    onupdate: (vnode) => {
        const { state } = vnode.attrs;
        // Reload data if symbol changed
        if (vnode.state.lastSymbol !== state.currentSymbol) {
            vnode.state.lastSymbol = state.currentSymbol;
            loadStockData(state.currentSymbol, state);
        }
    },

    view: (vnode) => {
        const { state } = vnode.attrs;
        const stockData = state.stockDataCache[state.currentSymbol];

        if (!stockData) {
            return m('div.flex.h-full.items-center.justify-center', [
                m('div.text-center', [
                    m('div.animate-spin.rounded-full.h-8.w-8.border-b-2.border-primary.mx-auto.mb-4'),
                    m('p.text-muted-foreground', 'Loading stock data...')
                ])
            ]);
        }

        return m('div#stockDetailView.flex.h-full', [
            // Main Content Area
            m('div.flex-1.overflow-auto.pr-4', [
                // Stock Header
                m(StockHeader, { stockData, symbol: state.currentSymbol }),

                // Technical Analysis Summary
                m(AnalysisSummary, { stockData }),

                // Chart Sections
                m(ChartSections, { symbol: state.currentSymbol, state })
            ]),

            // Resizable Divider
            m('div.w-1.bg-border.hover\\:bg-primary.cursor-col-resize.transition-colors'),

            // Fixed Right Sidebar - Stock Signals
            m(StockSignalsSidebar, { stockData, state })
        ]);
    }
};

const StockHeader = {
    view: (vnode) => {
        const { stockData } = vnode.attrs;

        // Transform data using DataTransformer
        const transformedData = DataTransformer.transformStockAnalysis(stockData);
        if (!transformedData) return m('div', 'No data available');

        const priceFormat = DataTransformer.formatPercentage(transformedData.price.changePercent);
        const trendIcon = DataTransformer.getTrendIcon(transformedData.trend.direction);
        const trendColorClass = DataTransformer.getTrendColorClass(transformedData.trend.direction);

        return m('div#stockHeader.mb-6', [
            m('div.bg-card.rounded-lg.p-6', [
                m('div.flex.items-center.justify-between.mb-4', [
                    m('div.flex.items-center.gap-4', [
                        m('h1.text-3xl.font-bold.text-foreground', transformedData.symbol),
                        m('div.text-2xl.font-semibold.text-foreground',
                            DataTransformer.formatPrice(transformedData.price.current)),
                        m(`div.flex.items-center.gap-1.${priceFormat.class}`, [
                            m(`span.material-symbols-outlined.text-lg`, trendIcon),
                            m('span.text-lg.font-medium',
                                `${DataTransformer.formatPrice(transformedData.price.change)} (${priceFormat.text})`)
                        ])
                    ]),
                    m('div.text-sm.text-muted-foreground', `Cập nhật: ${transformedData.lastTradingDate}`)
                ]),
                m('div.flex.items-center.gap-4.text-sm', [
                    m(`span.px-3.py-1.rounded-full.bg-muted.${trendColorClass}`,
                        `Xu hướng: ${transformedData.trend.direction} (${transformedData.trend.strength})`),
                    m('span.px-3.py-1.rounded-full.bg-muted.text-muted-foreground',
                        `Thị trường: ${transformedData.marketCondition}`),
                    m('span.px-3.py-1.rounded-full.bg-muted.text-muted-foreground',
                        `Độ tin cậy: ${transformedData.trend.confidence}`)
                ])
            ])
        ]);
    }
};

const AnalysisSummary = {
    view: (vnode) => {
        const { stockData } = vnode.attrs;

        // Transform data using DataTransformer
        const transformedData = DataTransformer.transformStockAnalysis(stockData);
        if (!transformedData) return m('div', 'No analysis data available');

        return m('div#analysisSummary.mb-6', [
            m('div.bg-card.border.border-border.rounded-lg.p-4', [
                m('h2.text-lg.font-semibold.mb-3.text-foreground', 'Tóm tắt phân tích kỹ thuật'),

                // Recommendation section
                m('div.mb-4.p-4.bg-muted\\/30.rounded-lg', [
                    m('div.flex.items-center.gap-2.mb-2', [
                        m('span.material-symbols-outlined.text-primary', 'lightbulb'),
                        m('span.font-semibold.text-sm', 'Khuyến nghị:'),
                        m(`span.px-2.py-1.rounded-full.text-xs.font-medium.${
                            transformedData.recommendation.action === 'Mua' ? 'bg-green-100 text-green-700' :
                            transformedData.recommendation.action === 'Bán' ? 'bg-red-100 text-red-700' :
                            'bg-yellow-100 text-yellow-700'
                        }`, transformedData.recommendation.action)
                    ]),
                    m('div.text-sm.text-foreground.leading-relaxed', transformedData.recommendation.summary)
                ]),

                // Trading zones
                m('div.grid.grid-cols-1.md\\:grid-cols-3.gap-4.mb-4', [
                    // Buy zones
                    m('div.p-3.bg-green-50.border.border-green-200.rounded-lg', [
                        m('div.flex.items-center.gap-2.mb-2', [
                            m('span.material-symbols-outlined.text-green-600.text-sm', 'trending_up'),
                            m('span.font-medium.text-sm.text-green-800', 'Vùng mua')
                        ]),
                        m('div.space-y-1',
                            transformedData.zones.buy.map(price =>
                                m('div.text-xs.text-green-700', DataTransformer.formatPrice(price))
                            )
                        )
                    ]),

                    // Stop loss zones
                    m('div.p-3.bg-red-50.border.border-red-200.rounded-lg', [
                        m('div.flex.items-center.gap-2.mb-2', [
                            m('span.material-symbols-outlined.text-red-600.text-sm', 'stop'),
                            m('span.font-medium.text-sm.text-red-800', 'Stop Loss')
                        ]),
                        m('div.space-y-1',
                            transformedData.zones.stopLoss.map(price =>
                                m('div.text-xs.text-red-700', DataTransformer.formatPrice(price))
                            )
                        )
                    ]),

                    // Take profit zones
                    m('div.p-3.bg-blue-50.border.border-blue-200.rounded-lg', [
                        m('div.flex.items-center.gap-2.mb-2', [
                            m('span.material-symbols-outlined.text-blue-600.text-sm', 'flag'),
                            m('span.font-medium.text-sm.text-blue-800', 'Take Profit')
                        ]),
                        m('div.space-y-1',
                            transformedData.zones.takeProfit.map(price =>
                                m('div.text-xs.text-blue-700', DataTransformer.formatPrice(price))
                            )
                        )
                    ])
                ]),

                // Risk/Reward ratios
                transformedData.riskReward.length > 0 && m('div.mt-4', [
                    m('h4.font-medium.text-sm.mb-2', 'Tỷ lệ Risk/Reward:'),
                    m('div.flex.gap-2',
                        transformedData.riskReward.map(ratio =>
                            m('span.px-2.py-1.bg-muted.rounded.text-xs', `1:${ratio}`)
                        )
                    )
                ])
            ])
        ]);
    }
};

const ChartSections = {
    view: (vnode) => {
        const { symbol, state } = vnode.attrs;

        return m('div#chartSections.space-y-4', [
            // Price Analysis Section
            m(ChartSection, {
                id: 'price-analysis',
                title: 'Phân tích giá',
                icon: 'trending_up',
                symbol,
                state,
                content: m('div', [
                    // Chart overlay controls
                    m('div.mb-4.p-3.bg-muted\\/30.rounded-lg', [
                        m('div.flex.items-center.justify-between.mb-3', [
                            m('h4.text-sm.font-medium.text-foreground', 'Hiển thị trên biểu đồ giá'),
                            m('button[type="button"].text-xs.px-2.py-1.rounded.bg-muted.hover\\:bg-muted\\/80.transition-colors', 'Reset All')
                        ]),
                        m('div.flex.flex-wrap.gap-2', [
                            ['RSI', 'Bollinger Bands', 'Ichimoku', 'Moving Averages'].map(overlay =>
                                m('label.flex.items-center.gap-2.cursor-pointer', [
                                    m('input[type="checkbox"].rounded.border-border'),
                                    m('span.text-sm', overlay)
                                ])
                            )
                        ])
                    ]),

                    // Main price chart - Import and use PriceChart component
                    m('div.chart-container.mb-4', [
                        m('div', 'Price chart will be implemented with PriceChart component')
                    ]),

                    // Volume chart
                    m('div.chart-container.mb-4', [
                        m('div', 'Volume chart will be implemented with VolumeChart component')
                    ]),

                    // Technical indicators grid
                    m('div#technicalIndicators.grid.grid-cols-1.lg\\:grid-cols-2.gap-4', [
                        // RSI Chart
                        m('div.chart-panel.bg-card.border.border-border.rounded-lg.p-4', [
                            m('h4.text-sm.font-medium.mb-2', 'RSI (14)'),
                            m('div', 'RSI chart will be implemented with IndicatorChart component')
                        ]),

                        // MACD Chart
                        m('div.chart-panel.bg-card.border.border-border.rounded-lg.p-4', [
                            m('h4.text-sm.font-medium.mb-2', 'MACD'),
                            m('div', 'MACD chart will be implemented with IndicatorChart component')
                        ])
                    ])
                ])
            }),

            // Technical Indicators Section
            m(ChartSection, {
                id: 'technical-indicators',
                title: 'Chỉ báo kỹ thuật',
                icon: 'analytics',
                symbol,
                state,
                content: m('div.grid.grid-cols-1.lg\\:grid-cols-2.gap-4', [
                    // Moving Averages
                    m('div.chart-panel.bg-card.border.border-border.rounded-lg.p-4', [
                        m('h4.text-sm.font-medium.mb-2', 'Moving Averages'),
                        m('div', 'MA chart will be implemented')
                    ]),

                    // Bollinger Bands
                    m('div.chart-panel.bg-card.border.border-border.rounded-lg.p-4', [
                        m('h4.text-sm.font-medium.mb-2', 'Bollinger Bands'),
                        m('div', 'BB chart will be implemented')
                    ]),

                    // Stochastic
                    m('div.chart-panel.bg-card.border.border-border.rounded-lg.p-4', [
                        m('h4.text-sm.font-medium.mb-2', 'Stochastic'),
                        m('div', 'Stochastic chart will be implemented')
                    ]),

                    // Williams %R
                    m('div.chart-panel.bg-card.border.border-border.rounded-lg.p-4', [
                        m('h4.text-sm.font-medium.mb-2', 'Williams %R'),
                        m('div', 'Williams %R chart will be implemented')
                    ])
                ])
            })
        ]);
    }
};

const ChartSection = {
    view: (vnode) => {
        const { id, title, icon, content } = vnode.attrs;

        return m(`div.chart-section[data-section="${id}"]`, [
            m('div.chart-section-header', [
                m('h3.chart-section-title', [
                    m(`svg.chart-section-icon[xmlns="http://www.w3.org/2000/svg"][width="20"][height="20"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]`, [
                        m('path[d="M3 3v18h18"]'),
                        m('path[d="m19 9-5 5-4-4-3 3"]')
                    ]),
                    title
                ]),
                m('button.chart-section-toggle[type="button"]', {
                    title: `Thu gọn/Mở rộng phần ${title.toLowerCase()}`
                }, [
                    m('svg.chart-section-chevron[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                        m('path[d="m6 9 6 6 6-6"]')
                    ])
                ])
            ]),
            m('div.chart-section-content', content)
        ]);
    }
};

const StockSignalsSidebar = {
    view: (vnode) => {
        const { stockData, state } = vnode.attrs;

        return m('div#stockSignalsSidebar.bg-card.border-l.border-border.overflow-y-auto', {
            style: 'width: 320px;'
        }, [
            // Header
            m('div.sticky.top-0.bg-card.border-b.border-border.p-4.z-10', [
                m('div.flex.items-center.justify-between', [
                    m('h2.text-lg.font-semibold.text-foreground', 'Tín hiệu kỹ thuật'),
                    m('button[type="button"].p-1.rounded-md.hover\\:bg-muted.transition-colors', {
                        title: 'Thu gọn/Mở rộng sidebar',
                        onclick: () => {
                            state.sidebarCollapsed = !state.sidebarCollapsed;
                        }
                    }, [
                        m('svg[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                            m('path[d="m15 18-6-6 6-6"]')
                        ])
                    ])
                ])
            ]),

            // Content
            m('div.p-4.space-y-4', [
                // Overall Recommendation
                m('div#overallRecommendation.bg-muted\\/50.rounded-lg.p-4', [
                    m('h3.font-semibold.mb-2', 'Khuyến nghị tổng thể'),
                    m('p.text-sm.text-muted-foreground', 'Phân tích tín hiệu kỹ thuật sẽ được hiển thị ở đây.')
                ]),

                // Key Indicators
                m('div#keyIndicators.space-y-3', [
                    m('h3.font-semibold', 'Chỉ báo chính'),
                    m('p.text-sm.text-muted-foreground', 'Các chỉ báo kỹ thuật quan trọng sẽ được hiển thị ở đây.')
                ]),

                // Trading Zones
                m('div#tradingZones.space-y-3', [
                    m('h3.font-semibold', 'Vùng giao dịch'),
                    m('p.text-sm.text-muted-foreground', 'Thông tin về vùng mua/bán sẽ được hiển thị ở đây.')
                ])
            ])
        ]);
    }
};

// Helper function to load stock data
async function loadStockData(symbol, state) {
    if (!symbol || symbol === 'Watchlist') {
        return;
    }

    try {
        // Check cache first
        if (state.stockDataCache[symbol]) {
            return;
        }

        // Load main stock data
        const response = await fetch(`analysis/${symbol}.json`);
        const stockData = await response.json();

        state.stockDataCache[symbol] = stockData;
        m.redraw();

        // Load price history
        try {
            const priceResponse = await fetch(`analysis/${symbol}_prices.json`);
            const priceHistory = await priceResponse.json();
            state.priceHistoryCache[symbol] = priceHistory;
            m.redraw();
        } catch (e) {
            console.log('No price history available for', symbol);
        }

    } catch (error) {
        console.error('Failed to load stock data for', symbol, error);
        state.showToast(`Failed to load data for ${symbol}`, 'error');
    }
}
