// MainContent Component
// Main content area that switches between watchlist and stock detail views

import { WatchlistView } from './WatchlistView.js';
import { StockDetailView } from './StockDetailView.js';

export const MainContent = {
    view: (vnode) => {
        const { state } = vnode.attrs;
        
        return m('main#main-contain-wrapper.flex-1.p-4.overflow-auto', [
            // Watchlist View
            state.currentView === 'watchlist' && m(WatchlistView, { state }),
            
            // Stock Detail View
            state.currentView === 'stock' && m(StockDetailView, { state })
        ]);
    }
};
