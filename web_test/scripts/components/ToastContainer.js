// ToastContainer Component
// Displays toast notifications

export const ToastContainer = {
    view: (vnode) => {
        const { state } = vnode.attrs;
        
        return m('div#toast-container.fixed.top-4.right-4.z-\\[100\\].flex.flex-col.gap-2.max-w-sm',
            state.toasts.map(toast => m(ToastItem, { toast, state }))
        );
    }
};

const ToastItem = {
    oncreate: (vnode) => {
        // Animate in
        const element = vnode.dom;
        element.style.transform = 'translateX(100%)';
        element.style.opacity = '0';
        
        requestAnimationFrame(() => {
            element.style.transition = 'all 0.3s ease-out';
            element.style.transform = 'translateX(0)';
            element.style.opacity = '1';
        });
    },
    
    view: (vnode) => {
        const { toast, state } = vnode.attrs;
        
        const baseClasses = 'relative w-full rounded-lg border p-4 shadow-lg backdrop-blur-sm';
        let toastClasses = baseClasses;
        let icon = '';
        
        switch (toast.type) {
            case 'success':
                toastClasses += ' bg-background border-green-200 text-green-800';
                icon = m('svg[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"].text-green-600', [
                    m('path[d="M22 11.08V12a10 10 0 1 1-5.93-9.14"]'),
                    m('polyline[points="22,4 12,14.01 9,11.01"]')
                ]);
                break;
            case 'error':
                toastClasses += ' bg-background border-red-200 text-red-800';
                icon = m('svg[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"].text-red-600', [
                    m('circle[cx="12"][cy="12"][r="10"]'),
                    m('line[x1="15"][x2="9"][y1="9"][y2="15"]'),
                    m('line[x1="9"][x2="15"][y1="9"][y2="15"]')
                ]);
                break;
            case 'warning':
                toastClasses += ' bg-background border-yellow-200 text-yellow-800';
                icon = m('svg[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"].text-yellow-600', [
                    m('path[d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"]'),
                    m('line[x1="12"][x2="12"][y1="9"][y2="13"]'),
                    m('dot[cx="12"][cy="17"][r="1"]')
                ]);
                break;
            default:
                toastClasses += ' bg-background border-border text-foreground';
                icon = m('svg[xmlns="http://www.w3.org/2000/svg"][width="16"][height="16"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"].text-blue-600', [
                    m('circle[cx="12"][cy="12"][r="10"]'),
                    m('path[d="m9 12 2 2 4-4"]')
                ]);
        }
        
        return m(`div.${toastClasses.replace(/\s+/g, '.')}`, {
            id: toast.id
        }, [
            m('div.flex.items-start.gap-3', [
                m('div.flex-shrink-0.mt-0\\.5', icon),
                m('div.flex-1.text-sm', toast.message),
                m('button[type="button"].flex-shrink-0.ml-2.text-muted-foreground.hover\\:text-foreground.transition-colors', {
                    onclick: () => removeToastWithAnimation(toast.id, state)
                }, [
                    m('svg[xmlns="http://www.w3.org/2000/svg"][width="14"][height="14"][viewBox="0 0 24 24"][fill="none"][stroke="currentColor"][stroke-width="2"][stroke-linecap="round"][stroke-linejoin="round"]', [
                        m('path[d="M18 6 6 18M6 6l12 12"]')
                    ])
                ])
            ])
        ]);
    }
};

function removeToastWithAnimation(toastId, state) {
    const toastElement = document.getElementById(toastId);
    if (!toastElement) return;
    
    toastElement.style.transition = 'all 0.3s ease-out';
    toastElement.style.transform = 'translateX(100%)';
    toastElement.style.opacity = '0';
    
    setTimeout(() => {
        state.removeToast(toastId);
    }, 300);
}
