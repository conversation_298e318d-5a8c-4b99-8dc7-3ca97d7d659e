// Sidebar Component
// Left sidebar with navigation icons

export const Sidebar = {
    view: (vnode) => {
        const { state } = vnode.attrs;
        
        return m('aside#main-sidebar-wrapper.bg-sidebar.border-r.overflow-hidden', [
            m('div.flex.h-full.w-full.flex-col', [
                // Header with logo
                m('div[data-sidebar="header"].p-2.gap-2', [
                    m('div.flex.aspect-square.size-8.items-center.justify-center.rounded-lg.bg-sidebar-primary.text-sidebar-primary-foreground', [
                        m('svg[xmlns="http://www.w3.org/2000/svg"][height="24px"][viewBox="0 -960 960 960"][width="24px"][fill="#FFFFFF"]', [
                            m('path[d="M368.5-195q-14.81 0-26.53-8.72-11.73-8.73-17.2-22.21L237.39-450H60.77v-60h218.77l88.92 230.92 179.39-455.38q5.45-13.48 17.14-22.2 11.7-8.72 26.51-8.72 14.81 0 26.53 8.72 11.73 8.72 17.2 22.2L723.38-510H900v60H681.23l-89.69-231.31-179.39 455.38q-5.45 13.48-17.14 22.21-11.7 8.72-26.51 8.72Z"]')
                        ])
                    ])
                ]),
                
                // Content with navigation items
                m('div[data-sidebar="content"].flex.min-h-0.flex-1.flex-col.gap-2.overflow-auto', [
                    m('div[data-sidebar="group"].relative.flex.w-full.min-w-0.flex-col.p-2', [
                        m('ul[data-sidebar="menu"].flex.w-full.min-w-0.flex-col.gap-1', [
                            // Dashboard button
                            m('li[data-sidebar="menu-item"].flex.flex-col.items-center.text-center', [
                                m('button[type="button"][title="Dashboard"][data-sidebar="menu-button"].text-sm.aspect-square.size-8.p-1.cursor-pointer', {
                                    onclick: () => {
                                        // Dashboard functionality can be added later
                                    }
                                }, [
                                    m('svg[xmlns="http://www.w3.org/2000/svg"][height="24px"][viewBox="0 -960 960 960"][width="24px"][fill="#1f1f1f"]', [
                                        m('path[d="M530-600v-220h290v220H530ZM140-460v-360h290v360H140Zm390 320v-360h290v360H530Zm-390 0v-220h290v220H140Zm60-380h170v-240H200v240Zm390 320h170v-240H590v240Zm0-460h170v-100H590v100ZM200-200h170v-100H200v100Zm170-320Zm220-140Zm0 220ZM370-300Z"]')
                                    ])
                                ])
                            ]),
                            
                            // Watchlist button (active)
                            m('li[data-sidebar="menu-item"].flex.flex-col.items-center.text-center', [
                                m('button[type="button"][title="Watchlist"][data-sidebar="menu-button"].bg-accent.text-sm.aspect-square.size-8.p-1.cursor-pointer', {
                                    onclick: () => {
                                        state.selectTab('Watchlist');
                                    }
                                }, [
                                    m('svg[xmlns="http://www.w3.org/2000/svg"][height="24px"][viewBox="0 -960 960 960"][width="24px"][fill="#1f1f1f"]', [
                                        m('path[d="M130-130v-65.77l60-60V-130h-60Zm160 0v-225.77l60-60V-130h-60Zm160 0v-285.77l60 61V-130h-60Zm160 0v-224.77l60-60V-130h-60Zm160 0v-385.77l60-60V-130h-60ZM130-351.23v-84.54l270-270 160 160 270-270v84.54l-270 270-160-160-270 270Z"]')
                                    ])
                                ])
                            ])
                        ])
                    ])
                ]),
                
                // Footer with settings
                m('div[data-sidebar="footer"].flex.flex-col.gap-2.p-2', [
                    m('ul[data-sidebar="menu"].flex.w-full.min-w-0.flex-col.gap-1', [
                        m('li[data-sidebar="menu-item"]', [
                            m('button[type="button"][title="Settings"][data-sidebar="menu-button"].text-sm.aspect-square.size-8.p-1.cursor-pointer', {
                                onclick: () => {
                                    // Settings functionality can be added later
                                }
                            }, [
                                m('svg[xmlns="http://www.w3.org/2000/svg"][height="24px"][viewBox="0 -960 960 960"][width="24px"][fill="#000000"]', [
                                    m('path[d="m387.69-100-15.23-121.85q-16.07-5.38-32.96-15.07-16.88-9.7-30.19-20.77L196.46-210l-92.3-160 97.61-73.77q-1.38-8.92-1.96-17.92-.58-9-.58-17.93 0-8.53.58-17.34t1.96-19.27L104.16-590l92.3-159.23 112.46 47.31q14.47-11.46 30.89-20.96t32.27-15.27L387.69-860h184.62l15.23 122.23q18 6.54 32.57 15.27 14.58 8.73 29.43 20.58l114-47.31L855.84-590l-99.15 74.92q2.15 9.69 2.35 18.12.19 8.42.19 16.96 0 8.15-.39 16.58-.38 8.42-2.76 19.27L854.46-370l-92.31 160-112.61-48.08q-14.85 11.85-30.31 20.96-15.46 9.12-31.69 14.89L572.31-100H387.69ZM440-160h78.62L533-267.15q30.62-8 55.96-22.73 25.35-14.74 48.89-37.89L737.23-286l39.39-68-86.77-65.38q5-15.54 6.8-30.47 1.81-14.92 1.81-30.15 0-15.62-1.81-30.15-1.8-14.54-6.8-29.7L777.38-606 738-674l-100.54 42.38q-20.08-21.46-48.11-37.92-28.04-16.46-56.73-23.31L520-800h-79.38l-13.24 106.77q-30.61 7.23-56.53 22.15-25.93 14.93-49.47 38.46L222-674l-39.38 68L269-541.62q-5 14.24-7 29.62t-2 32.38q0 15.62 2 30.62 2 15 6.62 29.62l-86 65.38L222-286l99-42q22.77 23.38 48.69 38.31 25.93 14.92 57.31 22.92L440-160Zm40.46-200q49.92 0 84.96-35.04 35.04-35.04 35.04-84.96 0-49.92-35.04-84.96Q530.38-600 480.46-600q-50.54 0-85.27 35.04T360.46-480q0 49.92 34.73 84.96Q429.92-360 480.46-360ZM480-480Z"]')
                                ])
                            ])
                        ])
                    ])
                ])
            ])
        ]);
    }
};
