// Main Layout Component
// Root component that renders the entire application layout

import { Sidebar } from './Sidebar.js';
import { TopBar } from './TopBar.js';
import { MainContent } from './MainContent.js';
import { CreateWatchlistDialog } from './dialogs/CreateWatchlistDialog.js';
import { WatchlistManagerDialog } from './dialogs/WatchlistManagerDialog.js';
import { ToastContainer } from './ToastContainer.js';

export const MainLayout = {
    view: (vnode) => {
        const { state } = vnode.attrs;
        
        return m('div.flex.flex-1.h-screen', [
            // Main Sidebar
            m(Sidebar, { state }),
            
            // Main Content Area
            m('div.flex.flex-col.w-full.overflow-hidden', [
                // Top Bar with Tabs
                m(TopBar, { state }),
                
                // Main Content
                m(MainContent, { state })
            ]),
            
            // Dialogs
            state.createWatchlistDialogOpen && m(CreateWatchlistDialog, { state }),
            state.watchlistManagerDialogOpen && m(WatchlistManagerDialog, { state }),
            
            // Toast Container
            m(ToastContainer, { state })
        ]);
    }
};
