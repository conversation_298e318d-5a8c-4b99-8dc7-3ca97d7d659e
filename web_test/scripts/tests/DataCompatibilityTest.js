// Data Compatibility Test Suite
// Tests the integration between server data and frontend components

import { DataTransformer } from '../utils/DataTransformer.js';

export class DataCompatibilityTest {
    constructor() {
        this.testResults = [];
        this.errors = [];
    }

    /**
     * Run all compatibility tests
     * @returns {Promise<Object>} Test results summary
     */
    async runAllTests() {
        console.log('🧪 Starting Data Compatibility Tests...');
        
        this.testResults = [];
        this.errors = [];

        // Test data loading
        await this.testDataLoading();
        
        // Test data transformation
        await this.testDataTransformation();
        
        // Test UI integration
        await this.testUIIntegration();
        
        // Test error handling
        await this.testErrorHandling();

        return this.generateReport();
    }

    /**
     * Test data loading from analysis files
     */
    async testDataLoading() {
        console.log('📁 Testing data loading...');
        
        const testSymbols = ['HPG', 'TCB', 'VCB', 'VIC', 'VHM'];
        const dataTypes = ['', '_prices', '_rsi', '_macd', '_ma', '_bb'];
        
        for (const symbol of testSymbols) {
            for (const dataType of dataTypes) {
                const fileName = `analysis/${symbol}${dataType}.json`;
                
                try {
                    const response = await fetch(fileName);
                    const data = await response.json();
                    
                    this.addTestResult(`Load ${fileName}`, true, `Successfully loaded ${Object.keys(data).length} fields`);
                } catch (error) {
                    this.addTestResult(`Load ${fileName}`, false, `Failed to load: ${error.message}`);
                }
            }
        }
    }

    /**
     * Test data transformation functions
     */
    async testDataTransformation() {
        console.log('🔄 Testing data transformation...');
        
        try {
            // Test stock analysis transformation
            const hpgData = await this.loadTestData('analysis/HPG.json');
            if (hpgData) {
                const transformed = DataTransformer.transformStockAnalysis(hpgData);
                
                this.addTestResult(
                    'Transform stock analysis',
                    transformed && transformed.symbol === 'HPG',
                    transformed ? 'Successfully transformed analysis data' : 'Transformation failed'
                );
                
                // Test required fields
                const requiredFields = ['symbol', 'price', 'trend', 'recommendation', 'zones'];
                for (const field of requiredFields) {
                    this.addTestResult(
                        `Required field: ${field}`,
                        transformed && transformed[field] !== undefined,
                        transformed && transformed[field] !== undefined ? 'Field present' : 'Field missing'
                    );
                }
            }

            // Test price history transformation
            const priceData = await this.loadTestData('analysis/HPG_prices.json');
            if (priceData) {
                const transformedPrices = DataTransformer.transformPriceHistory(priceData);
                
                this.addTestResult(
                    'Transform price history',
                    Array.isArray(transformedPrices) && transformedPrices.length > 0,
                    `Transformed ${transformedPrices.length} price points`
                );
                
                // Test price point structure
                if (transformedPrices.length > 0) {
                    const firstPoint = transformedPrices[0];
                    const priceFields = ['time', 'open', 'high', 'low', 'close'];
                    
                    for (const field of priceFields) {
                        this.addTestResult(
                            `Price field: ${field}`,
                            firstPoint[field] !== undefined,
                            firstPoint[field] !== undefined ? 'Field present' : 'Field missing'
                        );
                    }
                }
            }

            // Test technical indicator transformation
            const rsiData = await this.loadTestData('analysis/HPG_rsi.json');
            if (rsiData) {
                const transformedRSI = DataTransformer.transformTechnicalIndicator(rsiData, 'rsi');
                
                this.addTestResult(
                    'Transform RSI indicator',
                    Array.isArray(transformedRSI) && transformedRSI.length > 0,
                    `Transformed ${transformedRSI.length} RSI points`
                );
            }

        } catch (error) {
            this.addTestResult('Data transformation', false, `Error: ${error.message}`);
        }
    }

    /**
     * Test UI component integration with real data
     */
    async testUIIntegration() {
        console.log('🎨 Testing UI integration...');
        
        try {
            // Test data formatting functions
            const testPrice = 26985;
            const formattedPrice = DataTransformer.formatPrice(testPrice);
            this.addTestResult(
                'Price formatting',
                formattedPrice && formattedPrice !== 'N/A',
                `Formatted price: ${formattedPrice}`
            );

            const testPercent = 0.027163238836390588;
            const formattedPercent = DataTransformer.formatPercentage(testPercent);
            this.addTestResult(
                'Percentage formatting',
                formattedPercent && formattedPercent.text && formattedPercent.class,
                `Formatted: ${formattedPercent.text} with class ${formattedPercent.class}`
            );

            // Test trend icon mapping
            const trendDirections = ['tăng', 'giảm', 'sideway'];
            for (const direction of trendDirections) {
                const icon = DataTransformer.getTrendIcon(direction);
                const colorClass = DataTransformer.getTrendColorClass(direction);
                
                this.addTestResult(
                    `Trend mapping: ${direction}`,
                    icon && colorClass,
                    `Icon: ${icon}, Color: ${colorClass}`
                );
            }

            // Test data validation
            const validAnalysisData = { s: 'HPG', p: { c: 100 }, t: { d: 'up' } };
            const isValid = DataTransformer.validateData(validAnalysisData, 'analysis');
            this.addTestResult(
                'Data validation',
                isValid === true,
                'Analysis data validation works'
            );

        } catch (error) {
            this.addTestResult('UI integration', false, `Error: ${error.message}`);
        }
    }

    /**
     * Test error handling scenarios
     */
    async testErrorHandling() {
        console.log('⚠️ Testing error handling...');
        
        try {
            // Test with null data
            const nullResult = DataTransformer.transformStockAnalysis(null);
            this.addTestResult(
                'Null data handling',
                nullResult === null,
                'Correctly handles null input'
            );

            // Test with incomplete data
            const incompleteData = { s: 'TEST' }; // Missing required fields
            const incompleteResult = DataTransformer.transformStockAnalysis(incompleteData);
            this.addTestResult(
                'Incomplete data handling',
                incompleteResult && incompleteResult.symbol === 'TEST',
                'Handles incomplete data gracefully'
            );

            // Test with invalid file
            try {
                await fetch('analysis/NONEXISTENT.json');
                this.addTestResult('Invalid file handling', false, 'Should have thrown error');
            } catch (error) {
                this.addTestResult('Invalid file handling', true, 'Correctly handles missing files');
            }

            // Test data validation with invalid data
            const invalidData = { invalid: 'data' };
            const isInvalid = DataTransformer.validateData(invalidData, 'analysis');
            this.addTestResult(
                'Invalid data validation',
                isInvalid === false,
                'Correctly identifies invalid data'
            );

        } catch (error) {
            this.addTestResult('Error handling', false, `Unexpected error: ${error.message}`);
        }
    }

    /**
     * Load test data from file
     * @param {string} fileName - File to load
     * @returns {Promise<Object|null>} Loaded data or null
     */
    async loadTestData(fileName) {
        try {
            const response = await fetch(fileName);
            return await response.json();
        } catch (error) {
            console.warn(`Failed to load test data from ${fileName}:`, error);
            return null;
        }
    }

    /**
     * Add test result
     * @param {string} testName - Name of the test
     * @param {boolean} passed - Whether test passed
     * @param {string} details - Additional details
     */
    addTestResult(testName, passed, details) {
        this.testResults.push({
            name: testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        });

        if (!passed) {
            this.errors.push({ testName, details });
        }

        console.log(`${passed ? '✅' : '❌'} ${testName}: ${details}`);
    }

    /**
     * Generate test report
     * @returns {Object} Test summary
     */
    generateReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;

        const report = {
            summary: {
                total: totalTests,
                passed: passedTests,
                failed: failedTests,
                successRate: `${successRate}%`
            },
            results: this.testResults,
            errors: this.errors,
            recommendations: this.generateRecommendations()
        };

        console.log('\n📊 Test Summary:');
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${failedTests}`);
        console.log(`Success Rate: ${successRate}%`);

        if (this.errors.length > 0) {
            console.log('\n❌ Failed Tests:');
            this.errors.forEach(error => {
                console.log(`- ${error.testName}: ${error.details}`);
            });
        }

        return report;
    }

    /**
     * Generate recommendations based on test results
     * @returns {Array<string>} List of recommendations
     */
    generateRecommendations() {
        const recommendations = [];
        
        const failedDataLoading = this.errors.filter(e => e.testName.includes('Load'));
        if (failedDataLoading.length > 0) {
            recommendations.push('Some analysis files are missing. Run server analysis to generate missing data files.');
        }

        const failedTransformation = this.errors.filter(e => e.testName.includes('Transform'));
        if (failedTransformation.length > 0) {
            recommendations.push('Data transformation issues detected. Review DataTransformer implementation.');
        }

        const failedUI = this.errors.filter(e => e.testName.includes('formatting') || e.testName.includes('mapping'));
        if (failedUI.length > 0) {
            recommendations.push('UI integration issues found. Check component data binding and formatting functions.');
        }

        if (recommendations.length === 0) {
            recommendations.push('All tests passed! Data compatibility is excellent.');
        }

        return recommendations;
    }
}

// Export for use in other modules
export default DataCompatibilityTest;
