// Main Mithril.js Application Entry Point
// This replaces the vanilla JavaScript implementation with Mithril.js components

import { AppState } from './state/AppState.js';
import { MainLayout } from './components/MainLayout.js';

// Initialize application state
const appState = new AppState();

// Mount the main application component
m.mount(document.body, {
    view: () => m(MainLayout, { state: appState })
});

// Handle window resize for chart responsiveness
let resizeTimeout;
window.addEventListener('resize', () => {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
        if (appState.currentSymbol && appState.currentSymbol !== 'Watchlist') {
            // Trigger chart re-render
            appState.triggerChartResize();
        }
    }, 200);
});
