<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mithril.js Test</title>
    <script src="https://unpkg.com/mithril/mithril.js"></script>
</head>
<body>
    <div id="app"></div>
    
    <script>
        // Simple test component
        const TestComponent = {
            view: () => m('div', [
                m('h1', 'Mithril.js is working!'),
                m('p', 'This is a test to verify Mithril.js setup.')
            ])
        };
        
        // Mount the component
        m.mount(document.getElementById('app'), TestComponent);
    </script>
</body>
</html>
