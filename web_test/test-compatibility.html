<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>StockPal Data Compatibility Test</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <link href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" rel="stylesheet">
    <style type="text/tailwindcss">
        @theme {
            --color-clifford: #da373d;
        }
    </style>
    <link href="styles/shadcn.css" rel="stylesheet">
</head>
<body class="bg-background text-foreground">
    <div class="container mx-auto p-6">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold mb-2">StockPal Data Compatibility Test</h1>
                <p class="text-muted-foreground">Testing integration between server data and Mithril.js frontend</p>
            </div>

            <!-- Test Controls -->
            <div class="bg-card border border-border rounded-lg p-6 mb-6">
                <div class="flex items-center gap-4 mb-4">
                    <button id="runTestsBtn" class="bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors">
                        <span class="material-symbols-outlined inline-block mr-2">play_arrow</span>
                        Run All Tests
                    </button>
                    <button id="clearResultsBtn" class="bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90 transition-colors">
                        <span class="material-symbols-outlined inline-block mr-2">clear</span>
                        Clear Results
                    </button>
                    <div id="testStatus" class="flex items-center gap-2 text-sm text-muted-foreground">
                        <span class="material-symbols-outlined">info</span>
                        Ready to run tests
                    </div>
                </div>
                
                <!-- Progress Bar -->
                <div id="progressContainer" class="hidden">
                    <div class="flex items-center gap-2 mb-2">
                        <span class="text-sm font-medium">Progress:</span>
                        <span id="progressText" class="text-sm text-muted-foreground">0%</span>
                    </div>
                    <div class="w-full bg-secondary rounded-full h-2">
                        <div id="progressBar" class="bg-primary h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <!-- Test Results Summary -->
            <div id="summaryContainer" class="hidden bg-card border border-border rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Test Summary</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div id="totalTests" class="text-2xl font-bold text-foreground">0</div>
                        <div class="text-sm text-muted-foreground">Total Tests</div>
                    </div>
                    <div class="text-center">
                        <div id="passedTests" class="text-2xl font-bold text-green-600">0</div>
                        <div class="text-sm text-muted-foreground">Passed</div>
                    </div>
                    <div class="text-center">
                        <div id="failedTests" class="text-2xl font-bold text-red-600">0</div>
                        <div class="text-sm text-muted-foreground">Failed</div>
                    </div>
                    <div class="text-center">
                        <div id="successRate" class="text-2xl font-bold text-primary">0%</div>
                        <div class="text-sm text-muted-foreground">Success Rate</div>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            <div id="recommendationsContainer" class="hidden bg-card border border-border rounded-lg p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4">Recommendations</h2>
                <div id="recommendationsList" class="space-y-2"></div>
            </div>

            <!-- Detailed Results -->
            <div id="resultsContainer" class="hidden bg-card border border-border rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Detailed Results</h2>
                <div id="resultsList" class="space-y-2"></div>
            </div>

            <!-- Console Output -->
            <div class="bg-card border border-border rounded-lg p-6 mt-6">
                <h2 class="text-xl font-semibold mb-4">Console Output</h2>
                <div id="consoleOutput" class="bg-muted rounded-md p-4 font-mono text-sm max-h-96 overflow-y-auto">
                    <div class="text-muted-foreground">Console output will appear here...</div>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        import { DataCompatibilityTest } from './scripts/tests/DataCompatibilityTest.js';

        // Initialize test runner
        const testRunner = new DataCompatibilityTest();
        let isRunning = false;

        // DOM elements
        const runTestsBtn = document.getElementById('runTestsBtn');
        const clearResultsBtn = document.getElementById('clearResultsBtn');
        const testStatus = document.getElementById('testStatus');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const summaryContainer = document.getElementById('summaryContainer');
        const recommendationsContainer = document.getElementById('recommendationsContainer');
        const resultsContainer = document.getElementById('resultsContainer');
        const consoleOutput = document.getElementById('consoleOutput');

        // Override console.log to capture output
        const originalConsoleLog = console.log;
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            appendToConsole(args.join(' '));
        };

        function appendToConsole(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = 'text-xs mb-1';
            logEntry.innerHTML = `<span class="text-muted-foreground">[${timestamp}]</span> ${message}`;
            consoleOutput.appendChild(logEntry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function updateStatus(message, icon = 'info') {
            testStatus.innerHTML = `
                <span class="material-symbols-outlined">${icon}</span>
                ${message}
            `;
        }

        function showProgress(show = true) {
            progressContainer.classList.toggle('hidden', !show);
        }

        function updateProgress(percent) {
            progressBar.style.width = `${percent}%`;
            progressText.textContent = `${percent}%`;
        }

        function displaySummary(summary) {
            document.getElementById('totalTests').textContent = summary.total;
            document.getElementById('passedTests').textContent = summary.passed;
            document.getElementById('failedTests').textContent = summary.failed;
            document.getElementById('successRate').textContent = summary.successRate;
            summaryContainer.classList.remove('hidden');
        }

        function displayRecommendations(recommendations) {
            const list = document.getElementById('recommendationsList');
            list.innerHTML = '';
            
            recommendations.forEach(rec => {
                const item = document.createElement('div');
                item.className = 'flex items-start gap-2 p-3 bg-muted/50 rounded-md';
                item.innerHTML = `
                    <span class="material-symbols-outlined text-primary mt-0.5">lightbulb</span>
                    <span class="text-sm">${rec}</span>
                `;
                list.appendChild(item);
            });
            
            recommendationsContainer.classList.remove('hidden');
        }

        function displayResults(results) {
            const list = document.getElementById('resultsList');
            list.innerHTML = '';
            
            results.forEach(result => {
                const item = document.createElement('div');
                item.className = `flex items-start gap-3 p-3 rounded-md border ${
                    result.passed ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                }`;
                
                item.innerHTML = `
                    <span class="material-symbols-outlined ${
                        result.passed ? 'text-green-600' : 'text-red-600'
                    }">${result.passed ? 'check_circle' : 'error'}</span>
                    <div class="flex-1">
                        <div class="font-medium text-sm">${result.name}</div>
                        <div class="text-xs text-muted-foreground mt-1">${result.details}</div>
                    </div>
                `;
                list.appendChild(item);
            });
            
            resultsContainer.classList.remove('hidden');
        }

        // Event listeners
        runTestsBtn.addEventListener('click', async () => {
            if (isRunning) return;
            
            isRunning = true;
            runTestsBtn.disabled = true;
            runTestsBtn.innerHTML = '<span class="material-symbols-outlined inline-block mr-2 animate-spin">refresh</span>Running Tests...';
            
            updateStatus('Running compatibility tests...', 'play_arrow');
            showProgress(true);
            updateProgress(0);
            
            // Clear previous results
            consoleOutput.innerHTML = '<div class="text-muted-foreground">Starting tests...</div>';
            summaryContainer.classList.add('hidden');
            recommendationsContainer.classList.add('hidden');
            resultsContainer.classList.add('hidden');
            
            try {
                // Simulate progress updates
                const progressSteps = [25, 50, 75, 100];
                let stepIndex = 0;
                
                const progressInterval = setInterval(() => {
                    if (stepIndex < progressSteps.length) {
                        updateProgress(progressSteps[stepIndex]);
                        stepIndex++;
                    } else {
                        clearInterval(progressInterval);
                    }
                }, 500);
                
                // Run tests
                const report = await testRunner.runAllTests();
                
                // Display results
                displaySummary(report.summary);
                displayRecommendations(report.recommendations);
                displayResults(report.results);
                
                updateStatus(`Tests completed: ${report.summary.successRate} success rate`, 'check_circle');
                
            } catch (error) {
                updateStatus(`Test failed: ${error.message}`, 'error');
                appendToConsole(`❌ Error: ${error.message}`);
            } finally {
                isRunning = false;
                runTestsBtn.disabled = false;
                runTestsBtn.innerHTML = '<span class="material-symbols-outlined inline-block mr-2">play_arrow</span>Run All Tests';
                showProgress(false);
            }
        });

        clearResultsBtn.addEventListener('click', () => {
            consoleOutput.innerHTML = '<div class="text-muted-foreground">Console output will appear here...</div>';
            summaryContainer.classList.add('hidden');
            recommendationsContainer.classList.add('hidden');
            resultsContainer.classList.add('hidden');
            updateStatus('Ready to run tests', 'info');
        });

        // Initial setup
        appendToConsole('Data Compatibility Test Runner initialized');
        updateStatus('Ready to run tests');
    </script>
</body>
</html>
