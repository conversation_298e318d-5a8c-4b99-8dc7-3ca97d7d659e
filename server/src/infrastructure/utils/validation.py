"""
Validation utilities for StockPal.

This module provides common validation functions used across
the application to ensure data integrity and consistency.
"""

import re
from typing import Any, List, Optional
from src.domain.entities.stock_models import DataProvider
from src.domain.exceptions.stock_exceptions import (
    DataValidationException as ValidationException
)


def validate_symbol(symbol: str) -> str:
    """
    Validate and normalize a stock symbol.

    Args:
        symbol: Stock symbol to validate

    Returns:
        Normalized stock symbol (uppercase, trimmed)

    Raises:
        ValidationException: If symbol is invalid
    """
    if not symbol:
        raise ValidationException("Symbol cannot be empty", "symbol", symbol)

    # Normalize symbol
    normalized = symbol.strip().upper()

    # Check symbol format (Vietnamese stock symbols)
    # Format: 3-4 letters, optionally followed by numbers
    if not re.match(r'^[A-Z]{3,4}[0-9]*$', normalized):
        raise ValidationException(
            f"Invalid symbol format: {symbol}. "
            "Symbol must be 3-4 letters optionally followed by numbers",
            "symbol",
            symbol
        )

    return normalized


def validate_days_back(days: int) -> int:
    """
    Validate the number of days for historical data.

    Args:
        days: Number of days to validate

    Returns:
        Validated days value

    Raises:
        ValidationException: If days value is invalid
    """
    if not isinstance(days, int):
        raise ValidationException(
            f"Days must be an integer, got {type(days).__name__}",
            "days_back",
            str(days)
        )

    if days <= 0:
        raise ValidationException(
            f"Days must be positive, got {days}",
            "days_back",
            str(days)
        )

    if days > 3650:  # 10 years maximum
        raise ValidationException(
            f"Days cannot exceed 3650 (10 years), got {days}",
            "days_back",
            str(days)
        )

    return days


def validate_provider(provider: Any) -> DataProvider:
    """
    Validate and normalize a data provider.

    Args:
        provider: Data provider to validate (string or DataProvider enum)

    Returns:
        Validated DataProvider enum

    Raises:
        ValidationException: If provider is invalid
    """
    if isinstance(provider, DataProvider):
        return provider

    if isinstance(provider, str):
        provider_lower = provider.lower()
        try:
            return DataProvider(provider_lower)
        except ValueError:
            valid_providers = [p.value for p in DataProvider]
            raise ValidationException(
                f"Invalid provider: {provider}. "
                f"Valid providers are: {', '.join(valid_providers)}",
                "provider",
                provider
            )

    raise ValidationException(
        f"Provider must be string or DataProvider enum, "
        f"got {type(provider).__name__}",
        "provider",
        str(provider)
    )


def validate_price(price: float, field_name: str = "price") -> float:
    """
    Validate a price value.

    Args:
        price: Price value to validate
        field_name: Name of the field being validated

    Returns:
        Validated price value

    Raises:
        ValidationException: If price is invalid
    """
    if not isinstance(price, (int, float)):
        raise ValidationException(
            f"{field_name} must be a number, got {type(price).__name__}",
            field_name,
            str(price)
        )

    if price <= 0:
        raise ValidationException(
            f"{field_name} must be positive, got {price}",
            field_name,
            str(price)
        )

    if price > 1000000:  # 1 million maximum (reasonable for stock prices)
        raise ValidationException(
            f"{field_name} seems unreasonably high: {price}",
            field_name,
            str(price)
        )

    return float(price)


def validate_volume(volume: int, field_name: str = "volume") -> int:
    """
    Validate a volume value.

    Args:
        volume: Volume value to validate
        field_name: Name of the field being validated

    Returns:
        Validated volume value

    Raises:
        ValidationException: If volume is invalid
    """
    if not isinstance(volume, (int, float)):
        raise ValidationException(
            f"{field_name} must be a number, got {type(volume).__name__}",
            field_name,
            str(volume)
        )

    if volume < 0:
        raise ValidationException(
            f"{field_name} cannot be negative, got {volume}",
            field_name,
            str(volume)
        )

    return int(volume)


def validate_timestamp(timestamp: int, field_name: str = "timestamp") -> int:
    """
    Validate a timestamp value.

    Args:
        timestamp: Unix timestamp to validate
        field_name: Name of the field being validated

    Returns:
        Validated timestamp value

    Raises:
        ValidationException: If timestamp is invalid
    """
    if not isinstance(timestamp, int):
        raise ValidationException(
            f"{field_name} must be an integer, got {type(timestamp).__name__}",
            field_name,
            str(timestamp)
        )

    if timestamp <= 0:
        raise ValidationException(
            f"{field_name} must be positive, got {timestamp}",
            field_name,
            str(timestamp)
        )

    # Check if timestamp is reasonable (after 2000-01-01 and before 2100-01-01)
    min_timestamp = 946684800  # 2000-01-01
    max_timestamp = 4102444800  # 2100-01-01

    if timestamp < min_timestamp or timestamp > max_timestamp:
        raise ValidationException(
            f"{field_name} is outside reasonable range: {timestamp}",
            field_name,
            str(timestamp)
        )

    return timestamp


def validate_percentage(percentage: float, field_name: str = "percentage") -> float:
    """
    Validate a percentage value.

    Args:
        percentage: Percentage value to validate (-100 to 100)
        field_name: Name of the field being validated

    Returns:
        Validated percentage value

    Raises:
        ValidationException: If percentage is invalid
    """
    if not isinstance(percentage, (int, float)):
        raise ValidationException(
            f"{field_name} must be a number, got {type(percentage).__name__}",
            field_name,
            str(percentage)
        )

    if percentage < -100 or percentage > 100:
        raise ValidationException(
            f"{field_name} must be between -100 and 100, got {percentage}",
            field_name,
            str(percentage)
        )

    return float(percentage)


def validate_confidence_score(score: float) -> float:
    """
    Validate a confidence score (0 to 100).

    Args:
        score: Confidence score to validate

    Returns:
        Validated confidence score

    Raises:
        ValidationException: If score is invalid
    """
    if not isinstance(score, (int, float)):
        raise ValidationException(
            f"Confidence score must be a number, got {type(score).__name__}",
            "confidence_score",
            str(score)
        )

    if score < 0 or score > 100:
        raise ValidationException(
            f"Confidence score must be between 0 and 100, got {score}",
            "confidence_score",
            str(score)
        )

    return float(score)


def validate_symbol_list(symbols: List[str]) -> List[str]:
    """
    Validate a list of stock symbols.

    Args:
        symbols: List of symbols to validate

    Returns:
        List of validated and normalized symbols

    Raises:
        ValidationException: If any symbol is invalid
    """
    if not isinstance(symbols, list):
        raise ValidationException(
            f"Symbols must be a list, got {type(symbols).__name__}",
            "symbols",
            str(symbols)
        )

    if not symbols:
        raise ValidationException(
            "Symbol list cannot be empty",
            "symbols",
            "[]"
        )

    if len(symbols) > 100:  # Reasonable limit
        raise ValidationException(
            f"Too many symbols: {len(symbols)}. Maximum is 100",
            "symbols",
            str(len(symbols))
        )

    validated_symbols = []
    for i, symbol in enumerate(symbols):
        try:
            validated_symbol = validate_symbol(symbol)
            validated_symbols.append(validated_symbol)
        except ValidationException as e:
            raise ValidationException(
                f"Invalid symbol at index {i}: {e.message}",
                "symbols",
                symbol
            )

    # Check for duplicates
    unique_symbols = list(set(validated_symbols))
    if len(unique_symbols) != len(validated_symbols):
        raise ValidationException(
            "Duplicate symbols found in list",
            "symbols",
            str(validated_symbols)
        )

    return validated_symbols


def validate_indicator_name(name: str) -> str:
    """
    Validate a technical indicator name.

    Args:
        name: Indicator name to validate

    Returns:
        Validated indicator name

    Raises:
        ValidationException: If name is invalid
    """
    if not isinstance(name, str):
        raise ValidationException(
            f"Indicator name must be a string, got {type(name).__name__}",
            "indicator_name",
            str(name)
        )

    if not name.strip():
        raise ValidationException(
            "Indicator name cannot be empty",
            "indicator_name",
            name
        )

    # Normalize name
    normalized = name.strip().upper()

    # Check name format (letters, numbers, underscore)
    if not re.match(r'^[A-Z0-9_]+$', normalized):
        raise ValidationException(
            f"Invalid indicator name format: {name}. "
            "Name must contain only letters, numbers, and underscores",
            "indicator_name",
            name
        )

    return normalized
