# StockPal Architecture Documentation

## Overview

StockPal follows Clean Architecture principles with clear separation of concerns, dependency injection, and modular design. The system provides comprehensive stock analysis, technical indicators, and trading signal generation with support for multiple data providers (SSI, VietStock, CafeF). The architecture integrates a backend analysis engine with a modern web interface for real-time stock analysis and visualization.

## Clean Architecture Structure

```
server/
├── src/                           # Clean Architecture Implementation
│   ├── domain/                    # Enterprise Business Rules
│   │   ├── entities/              # Core business entities
│   │   │   ├── stock.py          # Stock entity
│   │   │   ├── price.py          # Price entity
│   │   │   └── analysis.py       # Analysis entity
│   │   ├── value_objects/         # Value objects
│   │   │   ├── technical_indicators.py
│   │   │   ├── trading_zones.py
│   │   │   └── risk_metrics.py
│   │   ├── services/              # Domain services
│   │   │   ├── trend_analyzer.py
│   │   │   └── signal_generator.py
│   │   └── repositories/          # Abstract repository interfaces
│   │       ├── stock_repository.py
│   │       └── price_repository.py
│   ├── application/               # Application Business Rules
│   │   ├── use_cases/             # Use case implementations
│   │   │   ├── analyze_stock.py
│   │   │   ├── fetch_data.py
│   │   │   └── batch_analysis.py
│   │   ├── services/              # Application services
│   │   │   ├── analysis_orchestrator.py
│   │   │   └── data_coordinator.py
│   │   └── interfaces/            # External service interfaces
│   │       ├── data_provider.py
│   │       └── cache_service.py
│   ├── infrastructure/            # Frameworks & Drivers
│   │   ├── persistence/           # Data persistence
│   │   │   ├── sqlite_stock_repository.py
│   │   │   └── sqlite_price_repository.py
│   │   ├── web/                   # Web framework (FastAPI)
│   │   │   ├── controllers/
│   │   │   ├── middleware/
│   │   │   └── routes/
│   │   ├── adapters/              # External service adapters
│   │   │   ├── ssi_adapter.py
│   │   │   ├── vietstock_adapter.py
│   │   │   └── cafef_adapter.py
│   │   └── config/                # Configuration
│   │       ├── settings.py
│   │       └── logging.py
│   └── main.py                    # Dependency injection & startup
├── stockpal/                      # Legacy components (preserved)
│   ├── core/                      # Core utilities & models
│   ├── data/                      # Data scrapers
│   ├── indicator/                 # Technical indicators
│   ├── db/                        # Database access
│   ├── ml/                        # ML components
│   └── sentiment/                 # Sentiment analysis
├── web_test/                      # Frontend web interface
│   ├── analysis/                  # Analysis data (JSON files)
│   ├── scripts/                   # JavaScript modules
│   ├── styles/                    # CSS stylesheets
│   └── index.html                 # Main interface
├── tests/                         # Test suite
├── db/                           # Database & logs
└── cache/                        # Data cache
```

## Architecture Layers

### 1. Domain Layer (Enterprise Business Rules)
**Location**: `src/domain/`
**Purpose**: Contains the core business logic and rules that are independent of any external concerns.

**Components**:
- **Entities**: Core business objects (Stock, Price, Analysis)
- **Value Objects**: Immutable objects representing concepts (TechnicalIndicator, TradingZone)
- **Domain Services**: Business logic that doesn't belong to a single entity
- **Repository Interfaces**: Abstract contracts for data access

### 2. Application Layer (Application Business Rules)
**Location**: `src/application/`
**Purpose**: Orchestrates the flow of data to and from entities, and directs those entities to use their business rules.

**Components**:
- **Use Cases**: Specific application operations (AnalyzeStock, FetchData, BatchAnalysis)
- **Application Services**: Coordinate multiple use cases
- **Interfaces**: Define contracts for external services

### 3. Infrastructure Layer (Frameworks & Drivers)
**Location**: `src/infrastructure/`
**Purpose**: Contains implementations of interfaces defined in inner layers and handles external concerns.

**Components**:
- **Persistence**: Database implementations (SQLite repositories)
- **Web**: HTTP controllers and routes (FastAPI)
- **Adapters**: External service implementations (SSI, VietStock, CafeF)
- **Configuration**: Settings and logging setup

### 4. Legacy Components (Preserved)
**Location**: `stockpal/`
**Purpose**: Existing components that provide specialized functionality and are actively used by the new architecture.

**Components**:
- **Technical Indicators**: All indicator implementations (`stockpal/indicator/`)
- **Data Scrapers**: Provider-specific scrapers (`stockpal/data/`)
- **Core Models**: Price and stock models (`stockpal/core/`)
- **Database Access**: DAOs and schema (`stockpal/db/`)

### 5. Web Interface Layer
**Location**: `web_test/`
**Purpose**: Frontend interface for visualizing stock analysis results and interacting with the system.

**Components**:
- **Main Interface**: Single-page application with tabbed stock views (`index.html`)
- **Analysis Data**: JSON files containing analysis results (`analysis/`)
- **JavaScript Modules**: Modular frontend logic (`scripts/`)
- **Styling**: TailwindCSS-based responsive design (`styles/`)

**Features**:
- **Responsive Design**: Full viewport layout with collapsible sidebars
- **Real-time Charts**: Interactive price charts with technical indicator overlays
- **Watchlist Management**: Dynamic watchlist creation and symbol management
- **Technical Analysis**: Comprehensive indicator display and signal visualization
- **Tab System**: Multi-stock analysis with persistent state management

## Data Flow Architecture

### Clean Architecture Data Flow

```
External Request → Infrastructure → Application → Domain → Application → Infrastructure → Response
```

1. **Request Entry**: HTTP request enters through Infrastructure layer (FastAPI controllers)
2. **Use Case Execution**: Application layer orchestrates the business operation
3. **Domain Logic**: Core business rules are applied in Domain layer
4. **Data Access**: Infrastructure layer handles data persistence and external APIs
5. **Response**: Result flows back through the layers to the client

### Stock Analysis Flow

```
User Request → Analyze Stock Use Case → Domain Services → Technical Indicators → Trading Signals
```

1. **Request Validation**: Validate stock symbol and parameters
2. **Data Retrieval**: Fetch historical price data from providers
3. **Indicator Calculation**: Apply technical analysis indicators
4. **Signal Generation**: Generate buy/sell/hold recommendations
5. **Risk Analysis**: Calculate stop-loss and take-profit levels
6. **Response Formation**: Format results for client consumption

### Web Interface Data Flow

```
Backend Analysis → JSON Export → File System → Web Interface → User Visualization
```

1. **Analysis Execution**: Run `refactored_main.py` to generate analysis data
2. **Data Export**: Analysis results exported to `web_test/analysis/` as JSON files
3. **File Structure**: Each symbol gets dedicated files (e.g., `HPG.json`, `HPG_rsi.json`)
4. **Web Loading**: JavaScript modules load analysis data from JSON files
5. **Visualization**: Charts and indicators rendered using analysis data
6. **User Interaction**: Real-time UI updates based on user selections

### Integration Data Format

**Main Analysis File** (`{SYMBOL}.json`):
```json
{
  "s": "HPG",                    // Symbol
  "ad": "2025-01-25",           // Analysis date
  "p": { "c": 25850, "cv": -1350, "cp": -0.05 },  // Price data
  "t": { "d": "giảm", "s": "trung bình", "c": "81.13%" },  // Trend
  "r": { "r": "Bán", "s": "Analysis summary..." },  // Recommendation
  "bz": [...],                  // Buy zones
  "slz": [...],                 // Stop loss zones
  "tpz": [...],                 // Take profit zones
  "ti": [...]                   // Technical indicators
}
```

**Indicator Files** (`{SYMBOL}_{indicator}.json`):
- RSI data: `HPG_rsi.json`
- MACD data: `HPG_macd.json`
- Bollinger Bands: `HPG_bb.json`
- Price data: `HPG_prices.json`

## Dependency Injection

### Container Configuration
**Location**: `src/main.py`

The main application file sets up dependency injection to ensure proper separation of concerns:

```python
# Domain Services
trend_analyzer = TrendAnalyzer()
signal_generator = SignalGenerator()

# Infrastructure
stock_repository = SqliteStockRepository()
price_repository = SqlitePriceRepository()
ssi_adapter = SSIAdapter()

# Application Services
analysis_orchestrator = AnalysisOrchestrator(
    trend_analyzer, signal_generator, stock_repository
)

# Use Cases
analyze_stock_use_case = AnalyzeStockUseCase(
    analysis_orchestrator, price_repository
)
```

## Legacy Integration

### Bridging Clean Architecture with Legacy Components

The StockPal system maintains backward compatibility with existing components while implementing Clean Architecture:

**Integration Strategy**:
- **Adapter Pattern**: Legacy components are wrapped in adapters that implement clean interfaces
- **Facade Pattern**: Complex legacy subsystems are simplified through facade interfaces
- **Dependency Injection**: Legacy components are injected as dependencies rather than hard-coded

**Legacy Component Usage**:
```python
# Clean Architecture Use Case using Legacy Components
class AnalyzeStockUseCase:
    def __init__(self,
                 trend_predictor: TrendPredictor,  # Legacy component
                 data_scraper: DataScraper,        # Legacy component
                 stock_repository: StockRepository  # Clean interface
                ):
        self.trend_predictor = trend_predictor
        self.data_scraper = data_scraper
        self.stock_repository = stock_repository
```

## Logging Architecture

### Structured Logging System
**Location**: `src/infrastructure/config/logging.py`

**Features**:
- **Per-Symbol Logs**: Separate log files for each stock symbol
- **Per-Indicator Logs**: Individual logs for each technical indicator
- **Centralized Configuration**: Single logging setup for entire application
- **Log Rotation**: Automatic file rotation with size and time limits

**Log Structure**:
```
server/db/logs/
├── {SYMBOL}/
│   ├── rsi.log          # RSI indicator logs for symbol
│   ├── macd.log         # MACD indicator logs for symbol
│   ├── analysis.log     # General analysis logs for symbol
│   └── errors.log       # Error logs for symbol
└── application.log      # General application logs
```

## Design Patterns

### 1. Factory Pattern
- **Usage**: DataScraper for provider selection
- **Benefit**: Easy addition of new data providers
- **Implementation**: Provider string maps to concrete scraper class

### 2. Template Method Pattern
- **Usage**: BaseIndicator for common indicator operations
- **Benefit**: Consistent interface across all indicators
- **Implementation**: Abstract methods for calculation, signals, trends

### 3. Strategy Pattern
- **Usage**: Adaptive weighting in TrendPredictor
- **Benefit**: Different strategies for different market conditions
- **Implementation**: Weight adjustment algorithms based on market state

### 4. Observer Pattern (Planned)
- **Usage**: Real-time data updates
- **Benefit**: Automatic UI updates when data changes
- **Implementation**: Event-driven architecture for live data

## Database Design

### File-Based Storage Structure

```
server/db/
├── cache/                  # Cached API responses
│   ├── ssi/               # SSI provider cache
│   ├── vietstock/         # VietStock provider cache
│   └── cafef/             # CafeF provider cache
├── logs/                  # Application logs
│   ├── {SYMBOL}/          # Symbol-specific logs
│   │   ├── rsi.log       # RSI indicator logs
│   │   ├── macd.log      # MACD indicator logs
│   │   └── analysis.log  # Analysis logs
│   └── stockpal.log      # General application log
└── exports/              # Exported data files
    ├── analysis/         # Analysis results
    └── reports/          # Generated reports
```

### Data Models

#### Core Data Structures
- **PriceData**: Base price information
- **DailyPrice/MinutePrice**: Time-specific price data
- **Stock**: Symbol metadata
- **EventData**: Corporate events
- **AnalysisResult**: Analysis output

## Performance Considerations

### 1. Caching Strategy
- **Level 1**: In-memory caching for active analysis
- **Level 2**: File-based caching for API responses
- **TTL**: 24 hours for daily data, 5 minutes for real-time data

### 2. Calculation Optimization
- **Vectorization**: Use NumPy for bulk calculations
- **Lazy Loading**: Calculate indicators only when needed
- **Parallel Processing**: Multi-threading for independent calculations

### 3. Memory Management
- **Data Streaming**: Process large datasets in chunks
- **Garbage Collection**: Explicit cleanup of large objects
- **Resource Pooling**: Reuse HTTP connections

## Security Architecture

### 1. Data Validation
- **Input Sanitization**: Validate all external data
- **Type Checking**: Strict type validation for calculations
- **Range Validation**: Ensure data values are within expected ranges

### 2. Error Handling
- **Graceful Degradation**: Continue operation with partial data
- **Error Logging**: Comprehensive error tracking
- **Fallback Mechanisms**: Alternative data sources on failure

### 3. Rate Limiting
- **Provider Limits**: Respect external API rate limits
- **Backoff Strategy**: Exponential backoff on failures
- **Circuit Breaker**: Temporary disable failing providers

## Scalability Design

### 1. Horizontal Scaling
- **Stateless Design**: No server-side session state
- **Load Balancing**: Distribute requests across instances
- **Database Sharding**: Partition data by symbol or date

### 2. Vertical Scaling
- **Resource Optimization**: Efficient memory and CPU usage
- **Caching Layers**: Reduce computational overhead
- **Algorithm Optimization**: Optimized indicator calculations

## Monitoring and Observability

### 1. Logging Strategy
- **Structured Logging**: JSON format for machine parsing
- **Log Aggregation**: Centralized log collection
- **Performance Metrics**: Calculation timing and accuracy

### 2. Health Checks
- **Data Provider Health**: Monitor API availability
- **Calculation Accuracy**: Validate indicator outputs
- **System Performance**: Track response times and resource usage

## Deployment Architecture

### 1. Development Environment
- **Local Development**: Python virtual environment
- **Testing**: Unit and integration test suites
- **Debugging**: Comprehensive logging and error tracking

### 2. Production Considerations (Future)
- **Containerization**: Docker for consistent deployment
- **Orchestration**: Kubernetes for scaling
- **CI/CD Pipeline**: Automated testing and deployment

## Integration Points

### 1. External APIs
- **SSI Securities**: Primary data provider
- **VietStock**: Alternative data source
- **CafeF**: Backup data provider

### 2. Future Integrations
- **Real-time Data**: WebSocket connections
- **Machine Learning**: TensorFlow/PyTorch integration
- **Cloud Storage**: AWS S3 or Google Cloud Storage

## Technology Stack

### Core Technologies
- **Language**: Python 3.7+
- **Data Processing**: NumPy, Pandas
- **HTTP Client**: Requests with session management
- **Web Framework**: Flask (planned)
- **Frontend**: HTML5, TailwindCSS, Vanilla JavaScript

### Development Tools
- **Testing**: pytest, unittest
- **Code Quality**: pylint, black, mypy
- **Documentation**: Sphinx (planned)
- **Version Control**: Git

### Dependencies
- **Required**: numpy, pandas, requests, dataclasses
- **Optional**: ta-lib, matplotlib, flask
- **Development**: pytest, pylint, black
