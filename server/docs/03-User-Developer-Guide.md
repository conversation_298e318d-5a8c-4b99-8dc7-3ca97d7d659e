# StockPal User & Developer Guide

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Quick Start](#quick-start)
3. [Installation & Setup](#installation--setup)
4. [User Guide](#user-guide)
5. [Developer Guide](#developer-guide)
6. [Clean Architecture Development](#clean-architecture-development)
7. [Configuration](#configuration)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)
10. [Contributing](#contributing)

## Architecture Overview

StockPal follows Clean Architecture principles with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Clean Architecture                       │
├─────────────────────────────────────────────────────────────┤
│  Domain Layer (src/domain/)                                │
│  ├── Entities: Stock, Price, Analysis                      │
│  ├── Value Objects: TechnicalIndicator, TradingZone        │
│  ├── Domain Services: TrendAnalyzer, SignalGenerator       │
│  └── Repository Interfaces: StockRepository, PriceRepo     │
├─────────────────────────────────────────────────────────────┤
│  Application Layer (src/application/)                      │
│  ├── Use Cases: AnalyzeStock, FetchData, BatchAnalysis     │
│  ├── Services: AnalysisOrchestrator, DataCoordinator       │
│  └── Interfaces: DataProvider, CacheService                │
├─────────────────────────────────────────────────────────────┤
│  Infrastructure Layer (src/infrastructure/)                │
│  ├── Web: FastAPI controllers, routes, middleware          │
│  ├── Persistence: SQLite repositories                      │
│  ├── Adapters: SSI, VietStock, CafeF adapters             │
│  └── Config: Settings, logging configuration               │
├─────────────────────────────────────────────────────────────┤
│  Legacy Components (stockpal/)                             │
│  ├── Technical Indicators: RSI, MACD, Bollinger Bands     │
│  ├── Data Scrapers: Provider-specific implementations      │
│  ├── Analysis Engine: TrendPredictor, SignalSynthesizer   │
│  └── Core Utilities: Logging, caching, data models        │
└─────────────────────────────────────────────────────────────┘
```

### Benefits of Clean Architecture

1. **Independence**: Business rules don't depend on external frameworks
2. **Testability**: Easy to test business logic in isolation
3. **Flexibility**: Easy to change external dependencies
4. **Maintainability**: Clear separation makes code easier to understand and modify

## Quick Start

### For Users (5-minute setup)

```bash
# 1. Clone the repository
git clone <repository-url>
cd stockpal

# 2. Install dependencies
cd server
pip install -r requirements.txt

# 3. Run stock analysis
python refactored_main.py

# 4. View results in web interface
cd ../web_test
python -m http.server 8000
# Open browser to http://localhost:8000
```

### For Developers (10-minute setup)

```bash
# 1. Clone and setup
git clone <repository-url>
cd stockpal

# 2. Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# 3. Install development dependencies
cd server
pip install -r requirements.txt

# 4. Run tests
python -m pytest tests/

# 5. Start development server
python refactored_main.py
```

> **📖 For detailed execution instructions, see [EXECUTION-GUIDE.md](./EXECUTION-GUIDE.md)**

## Stock Analysis Execution

### Running Stock Analysis

StockPal provides multiple ways to execute stock analysis and generate data for the web interface:

#### 1. Using the Refactored Main Application

**Basic Usage:**
```bash
cd server
python refactored_main.py
```

This runs a demonstration of the StockPal application with sample stocks (VIC, VHM, HPG, TCB, VCB) and generates analysis data for the web interface.

**Python API Usage:**
```python
from server.refactored_main import StockPalApplication

# Initialize application
app = StockPalApplication()

# Analyze single stock
analysis = app.analyze_stock("VIC", days_back=90)
print(f"Recommendation: {analysis['recommendation']}")
print(f"Trend: {analysis['trend']['direction']} ({analysis['trend']['strength']:.1%})")
print(f"Confidence: {analysis['confidence_score']:.1f}%")

# Batch analysis for multiple stocks
results = app.run_batch_analysis(symbols=["VIC", "HPG", "TCB"])
print(f"Analyzed: {results['successful_analyses']} stocks")

# ML prediction
prediction = app.get_ml_prediction("VIC", horizon_days=5)
print(f"Predicted price: {prediction['predicted_price']:.2f}")

# Backtesting
backtest = app.run_backtest("VIC", {"stop_loss": 0.05, "take_profit": 0.12})
print(f"Return: {backtest['total_return_percent']:.2f}%")
```

#### 2. Using Clean Architecture Components

**Direct Use Case Execution:**
```python
from src.main import create_app

# Initialize clean architecture container
app = create_app()

# Get analysis use case
analyze_use_case = app.get_analyze_stock_use_case("VIC")

# Execute analysis
result = analyze_use_case.execute()
```

### Data Output Format and Integration

#### Analysis Results Structure

The stock analysis generates JSON data in the following format for web interface consumption:

```json
{
  "s": "VIC",                    // Symbol
  "ad": "2025-05-25",           // Analysis date
  "ltd": "2023-11-23",          // Last trading date
  "p": {                        // Price information
    "c": 25850,                 // Current price
    "cv": -1350,                // Change value
    "cp": -0.05                 // Change percentage
  },
  "t": {                        // Trend analysis
    "d": "giảm",                // Direction (tăng/giảm/sideway)
    "s": "trung bình",          // Strength (yếu/trung bình/mạnh)
    "c": "81.13%"               // Confidence
  },
  "mc": "ranging",              // Market condition
  "r": {                        // Recommendation
    "r": "Bán",                 // Action (Mua/Bán/Giữ)
    "s": "=== PHÂN TÍCH KỸ THUẬT..."  // Summary text
  },
  "bz": [...],                  // Buy zones
  "slz": [...],                 // Stop loss zones
  "tpz": [...],                 // Take profit zones
  "rr": [...],                  // Risk/reward ratios
  "ti": [...]                   // Technical indicators
}
```

#### Output Locations

**1. Web Interface Data:**
- Location: `web_test/analysis/`
- Format: JSON files named `{SYMBOL}.json`
- Contains: Complete analysis results for web consumption
- Example: `web_test/analysis/HPG.json`, `web_test/analysis/HPG_rsi.json`

**2. Database Storage:**
- Location: `server/db/stock_data.db`
- Tables: `daily_prices`, `symbols`, `trading_dates`
- Format: SQLite database with normalized data

**3. Cache Files:**
- Location: `server/cache/`
- Structure: Organized by symbol and data provider
- Format: JSON cache files for API responses

**4. Log Files:**
- Location: `server/db/logs/`
- Structure: Per-symbol and per-indicator logs
- Format: Timestamped log entries for debugging

### Integration with Web Interface

#### Data Flow Process

1. **Backend Analysis Execution:**
   ```bash
   # Run analysis for specific symbols
   cd server
   python refactored_main.py
   ```

2. **Data Generation:**
   - Analysis results saved to `web_test/analysis/{SYMBOL}.json`
   - Technical indicators saved to `web_test/analysis/{SYMBOL}_{indicator}.json`
   - Price data saved to `web_test/analysis/{SYMBOL}_prices.json`

3. **Web Interface Consumption:**
   - Open `web_test/index.html` in browser
   - Interface automatically loads analysis data from JSON files
   - Real-time charts and indicators display analysis results

#### Web Interface Features

**Main Interface Components:**
- **Left Sidebar:** Symbol selection and navigation
- **Top Bar:** Tab system for multiple stocks
- **Main Area:** Charts and technical analysis
- **Right Sidebar:** Trading signals and recommendations

**Supported Analysis Views:**
- Price analysis with overlay options (RSI, Bollinger Bands, Ichimoku, Moving Averages)
- Momentum indicators (RSI, MACD, Stochastic)
- Volume analysis (OBV, ADI)
- Trend analysis (SAR, ADX, Ichimoku)

#### Running the Web Interface

```bash
# Option 1: Simple HTTP server
cd web_test
python -m http.server 8000
# Open browser to http://localhost:8000

# Option 2: Using Node.js (if available)
cd web_test
npx serve .
# Open browser to displayed URL
```

## Installation & Setup

### System Requirements

- **Python**: 3.7 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 1GB free space for cache and logs
- **Network**: Internet connection for data fetching

### Installation Steps

#### 1. Environment Setup

```bash
# Create virtual environment
python -m venv stockpal-env

# Activate environment
# On Linux/Mac:
source stockpal-env/bin/activate
# On Windows:
stockpal-env\Scripts\activate
```

#### 2. Install Dependencies

```bash
# Core dependencies
pip install numpy pandas requests dataclasses

# Optional dependencies for enhanced features
pip install matplotlib flask ta-lib

# Development dependencies (for contributors)
pip install pytest pylint black mypy
```

#### 3. Configuration

```bash
# Create configuration directory
mkdir -p server/config

# Copy example configuration
cp server/config/config.example.py server/config/config.py

# Edit configuration as needed
nano server/config/config.py
```

#### 4. Verify Installation

```bash
cd server
python -c "from stockpal.core import Constants; print('Installation successful!')"
```

## User Guide

### Basic Usage

#### 1. Analyzing a Single Stock

```python
from stockpal.data import DataScraper
from stockpal.analysis import TrendPredictor

# Fetch data for VCB stock
scraper = DataScraper("VCB", provider="ssi")
prices = scraper.fetch_prices()

# Analyze trends
predictor = TrendPredictor("VCB", prices)
analysis = predictor.analyze_trend()

print(f"Trend: {analysis['trend']}")
print(f"Confidence: {analysis['confidence']:.2%}")
```

#### 2. Getting Trading Signals

```python
from stockpal.indicator import RelativeStrengthIndex

# Calculate RSI
rsi = RelativeStrengthIndex("VCB", prices, period=14)
signals = rsi.get_signals()

for signal in signals:
    print(f"{signal['timestamp']}: {signal['signal_type']} - {signal['reason']}")
```

#### 3. Multiple Indicator Analysis

```python
from stockpal.indicator import RelativeStrengthIndex, MACD, BollingerBands

# Initialize indicators
rsi = RelativeStrengthIndex("VCB", prices)
macd = MACD("VCB", prices)
bb = BollingerBands("VCB", prices)

# Get recommendations
print(f"RSI: {rsi.get_recommendation()}")
print(f"MACD: {macd.get_recommendation()}")
print(f"Bollinger Bands: {bb.get_recommendation()}")
```

### Web Interface Usage

#### 1. Starting the Web Interface

```bash
cd web_test
python -m http.server 8000
# Open browser to http://localhost:8000
```

#### 2. Interface Features

- **Stock Selection**: Choose from watchlists or search symbols
- **Chart Visualization**: Interactive price charts with indicators
- **Technical Analysis**: Real-time indicator calculations
- **Signal Dashboard**: Trading signals with confidence levels
- **Watchlist Management**: Create and manage stock watchlists

#### 3. Keyboard Shortcuts

- `Ctrl + S`: Save current analysis
- `Ctrl + R`: Refresh data
- `Ctrl + F`: Search stocks
- `Esc`: Close dialogs

### Advanced Features

#### 1. Custom Indicator Parameters

```python
# Custom RSI with different period
rsi_custom = RelativeStrengthIndex("VCB", prices, period=21)

# Custom MACD parameters
macd_custom = MACD("VCB", prices, fast_period=8, slow_period=21, signal_period=5)

# Custom Bollinger Bands
bb_custom = BollingerBands("VCB", prices, period=20, std_dev=2.5)
```

#### 2. Backtesting

```python
from stockpal.backtesting import BacktestEngine

# Setup backtesting
backtest = BacktestEngine("VCB", prices, initial_capital=100000)

# Add strategy
strategy = backtest.add_rsi_strategy(period=14, overbought=70, oversold=30)

# Run backtest
results = backtest.run()
print(f"Total Return: {results['total_return']:.2%}")
print(f"Win Rate: {results['win_rate']:.2%}")
```

#### 3. Data Export

```python
# Export to Excel
from stockpal.utils import ExcelExporter

exporter = ExcelExporter()
exporter.export_analysis("VCB", analysis, "vcb_analysis.xlsx")

# Export to CSV
import pandas as pd
df = pd.DataFrame(prices)
df.to_csv("vcb_prices.csv", index=False)
```

## Developer Guide

### Project Structure

```
stockpal/
├── server/                 # Backend Python code
│   ├── stockpal/          # Main package
│   │   ├── core/          # Core utilities
│   │   ├── data/          # Data providers
│   │   ├── indicator/     # Technical indicators
│   │   ├── analysis/      # Analysis services
│   │   └── web/           # Web API (planned)
│   ├── tests/             # Test suite
│   └── config/            # Configuration files
├── web_test/              # Frontend interface
├── docs/                  # Documentation
└── requirements.txt       # Dependencies
```

### Development Workflow

#### 1. Setting Up Development Environment

```bash
# Clone repository
git clone <repository-url>
cd stockpal

# Setup development environment
python -m venv dev-env
source dev-env/bin/activate

# Install development dependencies
pip install -r requirements-dev.txt

# Setup pre-commit hooks
pre-commit install
```

#### 2. Code Style Guidelines

- **PEP 8**: Follow Python style guidelines
- **Type Hints**: Use type annotations for all functions
- **Docstrings**: Document all classes and methods
- **Imports**: Organize imports (stdlib, third-party, local)

```python
# Example of proper code style
from typing import List, Optional
import logging

from stockpal.core import PriceData
from .base import BaseIndicator

class ExampleIndicator(BaseIndicator):
    """
    Example indicator implementation.

    Args:
        symbol: Stock symbol
        prices: Historical price data
        period: Calculation period
    """

    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        super().__init__(symbol, prices, period=period)
        self.logger = logging.getLogger(__name__)
```

#### 3. Testing Guidelines

```python
# Example test structure
import pytest
from stockpal.indicator import RelativeStrengthIndex
from tests.fixtures import sample_price_data

class TestRSI:
    def test_rsi_calculation(self):
        """Test RSI calculation with known values."""
        rsi = RelativeStrengthIndex("TEST", sample_price_data)
        values = rsi.calculate()

        assert len(values) == len(sample_price_data)
        assert all(0 <= v <= 100 for v in values if v is not None)

    def test_rsi_signals(self):
        """Test RSI signal generation."""
        rsi = RelativeStrengthIndex("TEST", sample_price_data)
        signals = rsi.get_signals()

        assert isinstance(signals, list)
        for signal in signals:
            assert 'signal_type' in signal
            assert signal['signal_type'] in ['buy', 'sell', 'hold']
```

#### 4. Adding New Indicators

```python
# 1. Create new indicator file
# server/stockpal/indicator/my_indicator.py

from typing import List, Dict, Any
from stockpal.core import PriceData
from .base import BaseIndicator

class MyIndicator(BaseIndicator):
    """Custom indicator implementation."""

    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14):
        super().__init__(symbol, prices, period=period)

    def calculate(self) -> List[float]:
        """Implement calculation logic."""
        # Your calculation here
        pass

    def get_signals(self) -> List[Dict[str, Any]]:
        """Implement signal generation."""
        # Your signal logic here
        pass

# 2. Add to __init__.py
# server/stockpal/indicator/__init__.py
from .my_indicator import MyIndicator

# 3. Write tests
# tests/test_my_indicator.py
```

#### 5. Adding New Data Providers

```python
# 1. Create provider scraper
# server/stockpal/data/my_provider_scraper.py

from typing import override
from stockpal.core import Scraper, PriceData

class MyProviderScraper(Scraper):
    """Scraper for My Provider API."""

    @override
    def prices(self, timeframe_in_minute: bool = False) -> List[PriceData]:
        """Fetch price data from My Provider."""
        # Implementation here
        pass

# 2. Update DataScraper factory
# server/stockpal/data/data_scraper.py
from .my_provider_scraper import MyProviderScraper

class DataScraper:
    def __init__(self, symbol: str, provider: str = "ssi"):
        # Add new provider option
        if provider == "myprovider":
            self._scraper = MyProviderScraper(symbol)
```

### Debugging and Logging

#### 1. Enable Debug Logging

```python
from stockpal.core.logging_config import StockPalLogger

# Initialize with debug level
StockPalLogger.initialize(log_level="DEBUG")

# Get logger for specific component
logger = StockPalLogger.get_indicator_logger("rsi", "VCB")
logger.debug("Debug message")
```

#### 2. Log File Locations

```
server/db/logs/
├── stockpal.log           # General application log
├── VCB/                   # Symbol-specific logs
│   ├── rsi.log           # RSI indicator logs
│   ├── macd.log          # MACD indicator logs
│   └── analysis.log      # Analysis logs
└── data_ssi.log          # Data provider logs
```

#### 3. Performance Profiling

```python
import cProfile
import pstats

# Profile indicator calculation
def profile_rsi():
    rsi = RelativeStrengthIndex("VCB", prices)
    return rsi.calculate()

# Run profiler
cProfile.run('profile_rsi()', 'rsi_profile.stats')

# Analyze results
stats = pstats.Stats('rsi_profile.stats')
stats.sort_stats('cumulative').print_stats(10)
```

## Clean Architecture Development

### Working with Clean Architecture

#### 1. Understanding the Layers

**Domain Layer** (`src/domain/`):
- Contains business entities, value objects, and domain services
- No dependencies on external frameworks
- Pure business logic and rules

**Application Layer** (`src/application/`):
- Orchestrates use cases and business workflows
- Depends only on domain layer
- Contains application-specific business rules

**Infrastructure Layer** (`src/infrastructure/`):
- Implements interfaces defined in inner layers
- Contains framework-specific code
- Handles external concerns (database, web, APIs)

#### 2. Creating New Use Cases

```python
# src/application/use_cases/my_use_case.py
from dataclasses import dataclass
from typing import Dict, Any
from ..interfaces.data_provider import DataProvider
from ...domain.entities.stock import Stock

@dataclass
class MyUseCaseRequest:
    symbol: str
    parameter: str

@dataclass
class MyUseCaseResponse:
    result: Dict[str, Any]
    success: bool

class MyUseCase:
    def __init__(self, data_provider: DataProvider):
        self._data_provider = data_provider

    async def execute(self, request: MyUseCaseRequest) -> MyUseCaseResponse:
        # Use case logic here
        pass
```

#### 3. Implementing Repository Interfaces

```python
# src/infrastructure/persistence/sqlite_my_repository.py
from typing import List, Optional
from ...domain.repositories.my_repository import MyRepository
from ...domain.entities.my_entity import MyEntity

class SqliteMyRepository(MyRepository):
    def __init__(self, db_connection):
        self._db = db_connection

    async def save(self, entity: MyEntity) -> MyEntity:
        # SQLite-specific implementation
        pass

    async def find_by_id(self, entity_id: str) -> Optional[MyEntity]:
        # SQLite-specific implementation
        pass
```

#### 4. Creating Adapters for External Services

```python
# src/infrastructure/adapters/my_external_service_adapter.py
from typing import List
from ...application.interfaces.external_service import ExternalService
from ...domain.entities.data import Data

class MyExternalServiceAdapter(ExternalService):
    def __init__(self, api_client):
        self._client = api_client

    async def fetch_data(self, symbol: str) -> List[Data]:
        # Adapter implementation
        raw_data = self._client.get_data(symbol)
        return [self._convert_to_domain(item) for item in raw_data]

    def _convert_to_domain(self, raw_item) -> Data:
        # Convert external format to domain entity
        pass
```

#### 5. Dependency Injection Setup

```python
# src/main.py - Updated dependency container
class DependencyContainer:
    def __init__(self):
        self._setup_dependencies()

    def _setup_dependencies(self):
        # Infrastructure
        self._db_connection = self._create_db_connection()
        self._external_client = self._create_external_client()

        # Repositories
        self._stock_repository = SqliteStockRepository(self._db_connection)

        # Adapters
        self._data_provider = SSIAdapter(self._external_client)

        # Use Cases
        self._analyze_stock_use_case = AnalyzeStockUseCase(
            self._data_provider,
            self._stock_repository
        )

    def get_analyze_stock_use_case(self) -> AnalyzeStockUseCase:
        return self._analyze_stock_use_case
```

### Testing Clean Architecture

#### 1. Unit Testing Domain Entities

```python
# tests/domain/entities/test_stock.py
import pytest
from src.domain.entities.stock import Stock

class TestStock:
    def test_stock_creation(self):
        stock = Stock(
            symbol="VCB",
            name="Vietcombank",
            exchange="HOSE"
        )
        assert stock.symbol == "VCB"
        assert stock.is_valid_for_analysis()

    def test_invalid_stock_raises_error(self):
        with pytest.raises(ValueError):
            Stock(symbol="", name="Test", exchange="HOSE")
```

#### 2. Testing Use Cases

```python
# tests/application/use_cases/test_analyze_stock.py
import pytest
from unittest.mock import Mock
from src.application.use_cases.analyze_stock import AnalyzeStockUseCase, AnalysisRequest

class TestAnalyzeStockUseCase:
    def setup_method(self):
        self.mock_trend_predictor = Mock()
        self.mock_data_scraper = Mock()
        self.use_case = AnalyzeStockUseCase(
            self.mock_trend_predictor,
            self.mock_data_scraper
        )

    async def test_successful_analysis(self):
        # Setup mocks
        self.mock_data_scraper.get_historical_data.return_value = [...]
        self.mock_trend_predictor.predict_trend.return_value = {...}

        # Execute use case
        request = AnalysisRequest(symbol="VCB")
        result = await self.use_case.execute(request)

        # Assertions
        assert result.success
        assert result.symbol == "VCB"
```

#### 3. Integration Testing

```python
# tests/integration/test_stock_analysis_integration.py
import pytest
from src.main import create_app

class TestStockAnalysisIntegration:
    def setup_method(self):
        self.app = create_app()

    async def test_end_to_end_analysis(self):
        # Test the complete flow from use case to infrastructure
        use_case = self.app.get_analyze_stock_use_case()
        request = AnalysisRequest(symbol="VCB")

        result = await use_case.execute(request)

        assert result.success
        assert "trend_prediction" in result.trend_prediction
```

## Testing

### Test Structure

```
tests/
├── domain/
│   ├── entities/
│   ├── value_objects/
│   └── services/
├── application/
│   ├── use_cases/
│   └── services/
├── infrastructure/
│   ├── adapters/
│   ├── persistence/
│   └── web/
├── integration/
└── fixtures/
```

### Running Tests

```bash
# Run all tests
pytest tests/

# Run specific test categories
pytest tests/domain/          # Domain layer tests
pytest tests/application/     # Application layer tests
pytest tests/infrastructure/  # Infrastructure layer tests
pytest tests/integration/     # Integration tests

# Run with coverage
pytest --cov=src tests/

# Run with verbose output
pytest -v tests/
```

### Test Configuration

```python
# tests/conftest.py
import pytest
from src.main import DependencyContainer

@pytest.fixture
def app():
    return DependencyContainer()

@pytest.fixture
def sample_stock():
    from src.domain.entities.stock import Stock
    return Stock(
        symbol="VCB",
        name="Vietcombank",
        exchange="HOSE"
    )

@pytest.fixture
def sample_price_data():
    # Return sample price data for testing
    pass
```

## Configuration

### Environment Variables

```bash
# Data provider settings
STOCKPAL_DEFAULT_PROVIDER=ssi
STOCKPAL_CACHE_TTL=86400

# Logging settings
STOCKPAL_LOG_LEVEL=INFO
STOCKPAL_LOG_DIR=/path/to/logs

# API settings
STOCKPAL_REQUEST_TIMEOUT=30
STOCKPAL_MAX_RETRIES=3
```

### Configuration File

```python
# server/config/config.py

class Config:
    # Data provider settings
    DEFAULT_PROVIDER = "ssi"
    CACHE_TTL_HOURS = 24

    # Indicator defaults
    RSI_PERIOD = 14
    MACD_FAST = 12
    MACD_SLOW = 26
    MACD_SIGNAL = 9

    # Risk management
    DEFAULT_RISK_PERCENTAGE = 2.0
    MAX_POSITION_SIZE = 0.1  # 10% of portfolio

    # Performance settings
    MAX_CONCURRENT_REQUESTS = 5
    REQUEST_TIMEOUT = 30
```

## Troubleshooting

### Common Issues

#### 1. Import Errors

```bash
# Error: ModuleNotFoundError: No module named 'stockpal'
# Solution: Ensure you're in the server directory
cd server
python -m stockpal.examples.basic_analysis VCB
```

#### 2. Data Fetching Failures

```python
# Error: DataFetchError: Failed to fetch data
# Solution: Check network connection and provider status
from stockpal.data import DataScraper

scraper = DataScraper("VCB", provider="vietstock")  # Try different provider
```

#### 3. Insufficient Data Errors

```python
# Error: InsufficientDataError: Need at least 14 periods
# Solution: Use shorter period or fetch more historical data
rsi = RelativeStrengthIndex("VCB", prices, period=7)  # Shorter period
```

#### 4. Performance Issues

```python
# Issue: Slow indicator calculations
# Solution: Use vectorized operations and caching
import numpy as np

# Use NumPy for calculations
prices_array = np.array([p.close_price for p in prices])
```

### Getting Help

1. **Documentation**: Check API documentation and examples
2. **Logs**: Review log files for detailed error information
3. **Tests**: Run test suite to verify installation
4. **Issues**: Report bugs on the project repository

### Performance Optimization

#### 1. Caching

```python
# Enable caching for repeated analysis
from stockpal.core.cache import CacheManager

cache = CacheManager()
cache.enable_caching(ttl_hours=24)
```

#### 2. Batch Processing

```python
# Process multiple symbols efficiently
symbols = ["VCB", "VIC", "VHM", "HPG"]
results = {}

for symbol in symbols:
    scraper = DataScraper(symbol)
    prices = scraper.fetch_prices()
    predictor = TrendPredictor(symbol, prices)
    results[symbol] = predictor.analyze_trend()
```

## Contributing

### Contribution Guidelines

1. **Fork** the repository
2. **Create** a feature branch
3. **Write** tests for new functionality
4. **Follow** code style guidelines
5. **Submit** a pull request

### Code Review Process

1. All code must pass automated tests
2. Code coverage should be maintained above 80%
3. Documentation must be updated for new features
4. Performance impact should be considered

### Release Process

1. **Version Bump**: Update version numbers
2. **Changelog**: Document all changes
3. **Testing**: Run full test suite
4. **Documentation**: Update user guides
5. **Release**: Create tagged release
