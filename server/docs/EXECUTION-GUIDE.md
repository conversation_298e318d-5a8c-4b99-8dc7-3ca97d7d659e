# StockPal Execution Guide

## Quick Start

This guide provides step-by-step instructions for running StockPal's stock analysis functionality and viewing results in the web interface.

### Prerequisites

1. **Python Environment**
   ```bash
   cd server
   python --version  # Should be Python 3.7+
   ```

2. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify Installation**
   ```bash
   python -c "import numpy, pandas, requests; print('Dependencies OK')"
   ```

## Running Stock Analysis

### Method 1: Basic Analysis (Recommended)

**Step 1: Execute Analysis**
```bash
cd server
python refactored_main.py
```

**Expected Output:**
```
=== StockPal Application Demo ===
Analyzing stock: VIC
✓ Analysis completed for VIC
✓ Data exported to web_test/analysis/VIC.json

Analyzing stock: VHM
✓ Analysis completed for VHM
✓ Data exported to web_test/analysis/VHM.json

... (continues for HPG, TCB, VCB)

=== Analysis Summary ===
Successfully analyzed: 5 stocks
Failed: 0 stocks
Total execution time: 45.2 seconds
```

**Step 2: Verify Data Generation**
```bash
ls -la web_test/analysis/
# Should show files like: VIC.json, VHM.json, HPG.json, etc.
```

### Method 2: Custom Symbol Analysis

**Analyze Specific Stocks:**
```python
from server.refactored_main import StockPalApplication

app = StockPalApplication()

# Single stock analysis
result = app.analyze_stock("MSN", days_back=90)
print(f"Recommendation: {result['recommendation']}")

# Multiple stocks
symbols = ["MSN", "VRE", "GAS"]
batch_result = app.run_batch_analysis(symbols=symbols)
print(f"Completed: {batch_result['successful_analyses']}")
```

### Method 3: Advanced Features

**ML Prediction:**
```python
app = StockPalApplication()

# Get ML-based price prediction
prediction = app.get_ml_prediction("VIC", horizon_days=5)
print(f"Predicted price: {prediction['predicted_price']:.2f}")
print(f"Confidence: {prediction['confidence']:.1%}")
```

**Backtesting:**
```python
# Run strategy backtest
strategy = {"stop_loss": 0.05, "take_profit": 0.12}
backtest = app.run_backtest("VIC", strategy)
print(f"Total return: {backtest['total_return_percent']:.2f}%")
print(f"Win rate: {backtest['win_rate']:.1%}")
```

**Portfolio Optimization:**
```python
# Optimize portfolio allocation
symbols = ["VIC", "HPG", "TCB", "VCB"]
portfolio = app.optimize_portfolio(symbols, risk_tolerance="moderate")
print("Optimal allocations:")
for symbol, weight in portfolio['allocations'].items():
    print(f"  {symbol}: {weight:.1%}")
```

## Web Interface Usage

### Step 1: Start Web Server

**Option A: Python HTTP Server**
```bash
cd web_test
python -m http.server 8000
```

**Option B: Node.js (if available)**
```bash
cd web_test
npx serve .
```

### Step 2: Open Web Interface

1. Open browser to `http://localhost:8000`
2. The interface should load with the StockPal dashboard
3. Available stocks will appear in the left sidebar

### Step 3: Navigate the Interface

**Main Components:**
- **Left Sidebar**: Stock selection and watchlist management
- **Top Bar**: Tab system for multiple stocks
- **Main Area**: Charts and technical analysis
- **Right Sidebar**: Trading signals and recommendations

**Key Features:**
- Click stock symbols to open analysis tabs
- Use chart overlay options (RSI, Bollinger Bands, etc.)
- View technical indicators in the right sidebar
- Manage watchlists using the "Danh sách" button

## Data Output Structure

### Generated Files

After running analysis, the following files are created:

```
web_test/analysis/
├── VIC.json          # Main analysis data for VIC
├── VIC_rsi.json      # RSI indicator data
├── VIC_macd.json     # MACD indicator data
├── VIC_prices.json   # Price history data
├── HPG.json          # Main analysis data for HPG
├── HPG_rsi.json      # RSI indicator data
└── ...               # Similar files for other symbols
```

### Data Format

**Main Analysis File** (`{SYMBOL}.json`):
```json
{
  "s": "VIC",                    // Symbol
  "ad": "2025-01-25",           // Analysis date
  "ltd": "2023-11-23",          // Last trading date
  "p": {                        // Price information
    "c": 25850,                 // Current price
    "cv": -1350,                // Change value
    "cp": -0.05                 // Change percentage
  },
  "t": {                        // Trend analysis
    "d": "giảm",                // Direction
    "s": "trung bình",          // Strength
    "c": "81.13%"               // Confidence
  },
  "mc": "ranging",              // Market condition
  "r": {                        // Recommendation
    "r": "Bán",                 // Action
    "s": "Analysis summary..."  // Detailed summary
  },
  "bz": [...],                  // Buy zones
  "slz": [...],                 // Stop loss zones
  "tpz": [...],                 // Take profit zones
  "rr": [...],                  // Risk/reward ratios
  "ti": [...]                   // Technical indicators
}
```

## Troubleshooting

### Common Issues

**1. Import Errors**
```bash
# Error: ModuleNotFoundError
pip install -r requirements.txt
# Ensure you're in the server directory
```

**2. No Data Generated**
```bash
# Check if analysis completed successfully
python refactored_main.py
# Look for "✓ Analysis completed" messages
```

**3. Web Interface Not Loading**
```bash
# Verify files exist
ls web_test/analysis/
# Check browser console for errors
# Ensure HTTP server is running
```

**4. Empty Analysis Results**
```bash
# Check internet connection for data providers
# Verify symbols are valid Vietnamese stocks
# Check logs in server/db/logs/ for errors
```

### Debug Mode

**Enable Verbose Logging:**
```python
import logging
logging.basicConfig(level=logging.DEBUG)

app = StockPalApplication()
result = app.analyze_stock("VIC")
```

**Check Log Files:**
```bash
# View application logs
tail -f server/db/logs/stockpal.log

# View symbol-specific logs
tail -f server/db/logs/VIC/analysis.log
```

## Performance Tips

### Optimization

1. **Batch Processing**: Analyze multiple stocks in one run
2. **Cache Usage**: Subsequent runs use cached data (faster)
3. **Selective Analysis**: Only analyze needed symbols
4. **Background Processing**: Run analysis while using web interface

### Resource Usage

- **Memory**: ~100MB for 5 stocks analysis
- **Storage**: ~10MB per stock for full analysis data
- **Network**: ~1MB per stock for data fetching
- **Time**: ~10 seconds per stock for complete analysis

## Advanced Configuration

### Custom Data Providers

```python
# Use specific data provider
app = StockPalApplication()
app.set_data_provider("vietstock")  # or "ssi", "cafef"
```

### Analysis Parameters

```python
# Customize analysis parameters
result = app.analyze_stock(
    symbol="VIC",
    days_back=180,           # Historical data period
    include_zones=True,      # Include buy/sell zones
    include_risk_analysis=True,  # Include risk metrics
    ml_prediction=True       # Include ML predictions
)
```

### Export Options

```python
# Export to different formats
app.export_analysis("VIC", format="csv")
app.export_analysis("VIC", format="excel")
app.export_batch_results(format="json")
```

## Next Steps

1. **Explore Web Interface**: Try different stocks and indicators
2. **Customize Analysis**: Modify parameters for specific needs
3. **Integrate with Trading**: Use signals for trading decisions
4. **Monitor Performance**: Track analysis accuracy over time
5. **Extend Functionality**: Add custom indicators or strategies

For detailed API documentation, see `01-API-Documentation.md`.
For architecture details, see `02-Architecture-Documentation.md`.
For development guidance, see `03-User-Developer-Guide.md`.
