# StockPal API Documentation

## Overview

StockPal provides a comprehensive API for stock analysis, technical indicators, and trading signal generation following Clean Architecture principles. This document covers the Python API, data structures, usage examples, and integration with the web interface.

## Architecture Overview

StockPal follows Clean Architecture with clear separation of concerns:

- **Domain Layer** (`src/domain/`): Core business entities and rules
- **Application Layer** (`src/application/`): Use cases and business logic orchestration
- **Infrastructure Layer** (`src/infrastructure/`): Web controllers, data adapters, and external integrations
- **Legacy Components** (`stockpal/`): Existing specialized functionality integrated through adapters

## Main Application API

### StockPalApplication Class

The primary interface for stock analysis operations:

```python
from server.refactored_main import StockPalApplication

# Initialize application
app = StockPalApplication()
```

#### Core Methods

**analyze_stock(symbol, days_back=365, include_zones=True, include_risk_analysis=True)**
```python
analysis = app.analyze_stock("VIC", days_back=90)
# Returns: Complete analysis dictionary with recommendation, trend, indicators
```

**run_batch_analysis(symbols=None, test_mode=False, export_results=True)**
```python
results = app.run_batch_analysis(symbols=["VIC", "HPG", "TCB"])
# Returns: Batch analysis results with success/failure counts
```

**get_ml_prediction(symbol, horizon_days=5)**
```python
prediction = app.get_ml_prediction("VIC", horizon_days=5)
# Returns: ML-based price prediction with confidence
```

**run_backtest(symbol, strategy_params=None)**
```python
backtest = app.run_backtest("VIC", {"stop_loss": 0.05, "take_profit": 0.12})
# Returns: Backtesting results with performance metrics
```

**optimize_portfolio(symbols, risk_tolerance="moderate")**
```python
portfolio = app.optimize_portfolio(["VIC", "HPG", "TCB"], risk_tolerance="moderate")
# Returns: Portfolio optimization with allocations and expected return
```

## Data Output Format

### Analysis Results Structure

The stock analysis generates JSON data consumed by the web interface:

```json
{
  "s": "VIC",                    // Symbol
  "ad": "2025-05-25",           // Analysis date
  "ltd": "2023-11-23",          // Last trading date
  "p": {                        // Price information
    "c": 25850,                 // Current price
    "cv": -1350,                // Change value
    "cp": -0.05                 // Change percentage
  },
  "t": {                        // Trend analysis
    "d": "giảm",                // Direction (tăng/giảm/sideway)
    "s": "trung bình",          // Strength (yếu/trung bình/mạnh)
    "c": "81.13%"               // Confidence
  },
  "mc": "ranging",              // Market condition
  "r": {                        // Recommendation
    "r": "Bán",                 // Action (Mua/Bán/Giữ)
    "s": "=== PHÂN TÍCH KỸ THUẬT..."  // Summary text
  },
  "bz": [...],                  // Buy zones
  "slz": [...],                 // Stop loss zones
  "tpz": [...],                 // Take profit zones
  "rr": [...],                  // Risk/reward ratios
  "ti": [...]                   // Technical indicators
}
```

### Output Locations

**Web Interface Data:**
- Location: `web_test/analysis/`
- Format: JSON files named `{SYMBOL}.json`
- Contains: Complete analysis results for web consumption

**Database Storage:**
- Location: `server/db/stock_data.db`
- Tables: `daily_prices`, `symbols`, `trading_dates`
- Format: SQLite database with normalized data

**Cache Files:**
- Location: `server/cache/`
- Structure: Organized by symbol and data provider
- Format: JSON cache files for API responses

## Core Data Structures

### PriceData
Base class for all price data objects.

```python
@dataclass
class PriceData:
    symbol: str
    timestamp: datetime
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
```

### DailyPrice
Daily price data extending PriceData.

```python
@dataclass
class DailyPrice(PriceData):
    # Inherits all PriceData fields
    pass
```

### MinutePrice
Minute-level price data extending PriceData.

```python
@dataclass
class MinutePrice(PriceData):
    # Inherits all PriceData fields
    pass
```

### Stock
Stock symbol information.

```python
@dataclass
class Stock:
    symbol: str
    name: str
    exchange: str
    sector: Optional[str] = None
    industry: Optional[str] = None
```

## Data Scraper API

### DataScraper
Factory class for creating data scrapers for different providers.

```python
class DataScraper:
    def __init__(self, symbol: str, provider: Literal["ssi", "vietstock", "cafef"] = "ssi")
    def fetch_prices(self, timeframe_in_minute: bool = False) -> List[PriceData]
    def fetch_stock_symbols(self) -> List[Stock]
    def events(self) -> List[EventData]
    def orders(self)
```

#### Usage Examples

```python
# Create scraper for VietStock data
scraper = DataScraper("VCB", provider="vietstock")

# Fetch daily prices
daily_prices = scraper.fetch_prices(timeframe_in_minute=False)

# Fetch minute prices
minute_prices = scraper.fetch_prices(timeframe_in_minute=True)

# Get all available symbols
symbols = scraper.fetch_stock_symbols()
```

## Technical Indicators API

### BaseIndicator
Abstract base class for all technical indicators.

```python
class BaseIndicator:
    def __init__(self, symbol: str, prices: List[PriceData], **kwargs)
    def calculate(self) -> List[float]
    def get_signals(self) -> List[Dict[str, Any]]
    def get_trend(self) -> Dict[str, Any]
    def get_recommendation(self) -> str
```

### RSI (Relative Strength Index)

```python
class RelativeStrengthIndex(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 14)
    def calculate(self) -> List[float]
    def get_signals(self, overbought: float = 70, oversold: float = 30) -> List[Dict[str, Any]]
    def get_trend(self) -> Dict[str, Any]
    def get_recommendation(self) -> str
```

#### Usage Example

```python
# Initialize RSI with 14-period
rsi = RelativeStrengthIndex("VCB", price_data, period=14)

# Calculate RSI values
rsi_values = rsi.calculate()

# Get trading signals
signals = rsi.get_signals(overbought=70, oversold=30)

# Get trend analysis
trend = rsi.get_trend()

# Get recommendation
recommendation = rsi.get_recommendation()
```

#### Signal Structure

```python
{
    "timestamp": datetime,
    "signal_type": "buy" | "sell" | "hold",
    "strength": float,  # 0.0 to 1.0
    "price": float,
    "rsi_value": float,
    "reason": str
}
```

#### Trend Structure

```python
{
    "trend": "uptrend" | "downtrend" | "sideways",
    "confidence": float  # 0.0 to 1.0
}
```

### MACD (Moving Average Convergence Divergence)

```python
class MACD(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData],
                 fast_period: int = 12, slow_period: int = 26, signal_period: int = 9)
    def calculate(self) -> Dict[str, List[float]]
    def get_signals(self) -> List[Dict[str, Any]]
```

#### Usage Example

```python
macd = MACD("VCB", price_data, fast_period=12, slow_period=26, signal_period=9)
macd_data = macd.calculate()
# Returns: {"macd": [...], "signal": [...], "histogram": [...]}
```

### Moving Averages

```python
class SimpleMovingAverage(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 20)

class ExponentialMovingAverage(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData], period: int = 20)
```

### Bollinger Bands

```python
class BollingerBands(BaseIndicator):
    def __init__(self, symbol: str, prices: List[PriceData],
                 period: int = 20, std_dev: float = 2.0)
    def calculate(self) -> Dict[str, List[float]]
    # Returns: {"upper": [...], "middle": [...], "lower": [...]}
```

## Analysis Services API

### TrendPredictor
Main analysis engine for trend prediction and signal synthesis.

```python
class TrendPredictor:
    def __init__(self, symbol: str, prices: List[PriceData], risk_percentage: float = 2.0)
    def analyze_trend(self) -> Dict[str, Any]
    def get_trading_signals(self) -> List[Dict[str, Any]]
    def calculate_price_targets(self) -> Dict[str, float]
    def get_market_condition(self) -> str
```

#### Usage Example

```python
predictor = TrendPredictor("VCB", price_data, risk_percentage=2.0)

# Get comprehensive trend analysis
trend_analysis = predictor.analyze_trend()

# Get trading signals
signals = predictor.get_trading_signals()

# Calculate price targets
targets = predictor.calculate_price_targets()
# Returns: {"entry": 85.5, "stop_loss": 82.0, "take_profit": 92.0}
```

## HTTP Service API

### HttpJsonService
Service for making HTTP requests and handling JSON responses.

```python
class HttpJsonService:
    def __init__(self, base_url: str, headers: Optional[Dict[str, str]] = None)
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict
    def post(self, endpoint: str, data: Optional[Dict] = None) -> Dict
```

### HttpXlsxService
Service for downloading and processing Excel files.

```python
class HttpXlsxService:
    def __init__(self, base_url: str)
    def download_xlsx(self, endpoint: str, params: Optional[Dict] = None) -> pd.DataFrame
```

## Error Handling

### Common Exceptions

```python
class InsufficientDataError(Exception):
    """Raised when not enough data is available for calculation"""

class InvalidParameterError(Exception):
    """Raised when invalid parameters are provided"""

class DataFetchError(Exception):
    """Raised when data fetching fails"""
```

### Error Response Format

```python
{
    "error": True,
    "message": "Error description",
    "error_type": "InsufficientDataError",
    "details": {
        "required_periods": 14,
        "available_periods": 10
    }
}
```

## Configuration

### Logging Configuration

```python
from stockpal.core.logging_config import StockPalLogger

# Initialize logging
StockPalLogger.initialize(log_level="INFO")

# Get indicator-specific logger
logger = StockPalLogger.get_indicator_logger("rsi", "VCB")

# Get data operation logger
data_logger = StockPalLogger.get_data_logger("ssi", "VCB")
```

### Constants

```python
class Constants:
    DEFAULT_RSI_PERIOD = 14
    DEFAULT_MACD_FAST = 12
    DEFAULT_MACD_SLOW = 26
    DEFAULT_MACD_SIGNAL = 9
    DEFAULT_BB_PERIOD = 20
    DEFAULT_BB_STD_DEV = 2.0

    OVERBOUGHT_THRESHOLD = 70
    OVERSOLD_THRESHOLD = 30

    CACHE_EXPIRY_HOURS = 24
```

## Rate Limits and Best Practices

### Rate Limits
- SSI: 100 requests per minute
- VietStock: 60 requests per minute
- CafeF: 120 requests per minute

### Best Practices
1. **Caching**: Use built-in caching to avoid redundant API calls
2. **Error Handling**: Always wrap API calls in try-catch blocks
3. **Logging**: Use the provided logging system for debugging
4. **Data Validation**: Validate input data before processing
5. **Performance**: Use appropriate timeframes for analysis

## Examples and Tutorials

### Complete Analysis Workflow

```python
from stockpal.data import DataScraper
from stockpal.indicator import RelativeStrengthIndex, MACD
from stockpal.analysis import TrendPredictor

# 1. Fetch data
scraper = DataScraper("VCB", provider="ssi")
prices = scraper.fetch_prices()

# 2. Calculate indicators
rsi = RelativeStrengthIndex("VCB", prices)
macd = MACD("VCB", prices)

# 3. Analyze trends
predictor = TrendPredictor("VCB", prices)
analysis = predictor.analyze_trend()

# 4. Get recommendations
recommendation = rsi.get_recommendation()
signals = predictor.get_trading_signals()

print(f"Recommendation: {recommendation}")
print(f"Signals: {signals}")
```
