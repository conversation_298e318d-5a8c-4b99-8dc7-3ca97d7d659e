# StockPal Refactored Main Application Summary

## Overview

The `refactored_main.py` file has been successfully created and demonstrates how to use the StockPal clean architecture components. This application provides a working interface to the stock analysis system with fallback demo data when real data is not available.

## Key Features

### 1. Clean Architecture Integration
- Uses the clean architecture container from `src/main.py`
- Attempts to use real analysis components but gracefully falls back to demo data
- Demonstrates proper dependency injection and separation of concerns

### 2. Stock Analysis
- **analyze_stock()**: Analyzes individual stocks with comprehensive data
- Generates realistic demo data with randomized variations
- Exports analysis results to web interface JSON files
- Includes price data, trend analysis, recommendations, and trading zones

### 3. Batch Processing
- **run_batch_analysis()**: Processes multiple stocks in batch
- Tracks successful and failed analyses
- Provides comprehensive reporting

### 4. ML Predictions
- **get_ml_prediction()**: Provides ML-based price predictions
- Includes confidence scores and prediction horizons
- Simplified demo implementation ready for real ML integration

### 5. Backtesting
- **run_backtest()**: Runs strategy backtests
- Provides performance metrics (return, win rate, Sharpe ratio, etc.)
- Configurable strategy parameters

### 6. Portfolio Optimization
- **optimize_portfolio()**: Optimizes portfolio allocations
- Supports different risk tolerance levels
- Provides expected returns and volatility metrics

## Data Export

The application automatically exports analysis data to the web interface:
- Creates `../web_test/analysis/` directory
- Exports individual stock analysis as JSON files
- Compatible with existing web interface format

## Demo Data Structure

Each stock analysis includes:
```json
{
  "s": "SYMBOL",           // Stock symbol
  "ad": "2025-05-26",      // Analysis date
  "ltd": "2025-05-26",     // Last trading date
  "p": {                   // Price data
    "c": 25000,            // Current price
    "cv": -500,            // Change value
    "cp": -0.02            // Change percentage
  },
  "t": {                   // Trend analysis
    "d": "sideway",        // Direction
    "s": "trung bình",     // Strength
    "c": "75.0%"           // Confidence
  },
  "mc": "ranging",         // Market condition
  "r": {                   // Recommendation
    "r": "Giữ",            // Recommendation
    "s": "Analysis summary"
  },
  "bz": [24500, 24800],    // Buy zones
  "slz": [23000, 23500],   // Stop loss zones
  "tpz": [26000, 26500],   // Take profit zones
  "rr": [1.2, 1.5],       // Risk/reward ratios
  "ti": []                 // Technical indicators
}
```

## Usage

### Running the Demo
```bash
cd server
python refactored_main.py
```

### Using as a Module
```python
from refactored_main import StockPalApplication

app = StockPalApplication()

# Analyze a single stock
result = app.analyze_stock("VIC")

# Run batch analysis
batch_results = app.run_batch_analysis(["VIC", "VHM", "HPG"])

# Get ML prediction
prediction = app.get_ml_prediction("VIC", horizon_days=5)

# Run backtest
backtest = app.run_backtest("VIC")

# Optimize portfolio
portfolio = app.optimize_portfolio(["VIC", "VHM", "HPG"])
```

## Integration Points

### Clean Architecture
- Integrates with `src/main.py` container
- Uses clean architecture use cases when available
- Graceful fallback to demo data when real components fail

### Web Interface
- Exports data to `../web_test/analysis/` for web consumption
- Compatible with existing web interface JSON format
- Automatic file creation and updates

### Logging
- Uses StockPal logging configuration
- Comprehensive logging for debugging and monitoring
- Separate log levels for different operations

## Error Handling

- Graceful handling of missing price data
- Fallback to demo data when clean architecture fails
- Comprehensive error logging and reporting
- Non-blocking error handling for batch operations

## Future Enhancements

1. **Real Data Integration**: Replace demo data with actual market data
2. **ML Model Integration**: Connect to real ML prediction models
3. **Advanced Backtesting**: Implement more sophisticated backtesting strategies
4. **Real-time Updates**: Add real-time data streaming capabilities
5. **Performance Optimization**: Add caching and performance monitoring

## Testing

The application includes a comprehensive demo that tests all major features:
- Batch analysis for 5 demo symbols
- Individual stock analysis
- ML predictions
- Backtesting
- Portfolio optimization

All tests pass successfully and generate appropriate demo data for the web interface.

## Files Generated

When running the application, the following files are created:
- `../web_test/analysis/VIC.json`
- `../web_test/analysis/VHM.json`
- `../web_test/analysis/HPG.json`
- `../web_test/analysis/TCB.json`
- `../web_test/analysis/VCB.json`

These files are immediately available for consumption by the web interface.
